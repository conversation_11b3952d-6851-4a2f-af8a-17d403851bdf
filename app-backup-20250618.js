let currentTransformedData = null;

// 内置简化的图布局算法，替代 dagre 库
class SimpleGraphLayout {
    constructor() {
        this.graphlib = {
            Graph: function() {
                return new SimpleGraph();
            }
        };
    }

    layout(graph) {
        const nodes = graph.nodes();
        const edges = graph.edges();

        if (nodes.length === 0) return;

        // 计算每个节点的层级（基于拓扑排序）
        const levels = this.calculateLevels(nodes, edges, graph);

        // 为每个层级分配节点并优化排序
        const levelNodes = this.organizeLevels(levels, edges, graph);

        // 计算布局参数
        const config = graph.graph();
        const nodeWidth = 252;
        const nodeHeight = 40;
        const rankSep = config.ranksep || 100;
        const nodeSep = config.nodesep || 50;

        // 执行布局
        this.positionNodes(levelNodes, graph, nodeWidth, nodeHeight, rankSep, nodeSep);
    }

    organizeLevels(levels, edges, graph) {
        const levelNodes = {};

        // 按层级分组
        Object.entries(levels).forEach(([nodeId, level]) => {
            if (!levelNodes[level]) levelNodes[level] = [];
            levelNodes[level].push(nodeId);
        });

        // 对每个层级内的节点进行优化排序
        Object.keys(levelNodes).forEach(level => {
            levelNodes[level] = this.optimizeLevelOrder(levelNodes[level], edges, parseInt(level));
        });

        return levelNodes;
    }

    optimizeLevelOrder(nodesInLevel, edges, currentLevel) {
        if (nodesInLevel.length <= 1) return nodesInLevel;

        // 计算每个节点的"权重"以优化排序
        const nodeWeights = {};

        nodesInLevel.forEach(nodeId => {
            let weight = 0;
            let connectionCount = 0;

            // 计算上游连接的影响
            edges.forEach(edge => {
                if (edge.w === nodeId) {
                    weight += 100; // 入边权重
                    connectionCount++;
                }
                if (edge.v === nodeId) {
                    weight += 50;  // 出边权重
                    connectionCount++;
                }
            });

            // 连接数越多，权重越高
            nodeWeights[nodeId] = weight + connectionCount * 10;
        });

        // 按权重排序
        return nodesInLevel.sort((a, b) => nodeWeights[b] - nodeWeights[a]);
    }

    positionNodes(levelNodes, graph, nodeWidth, nodeHeight, rankSep, nodeSep) {
        const levelKeys = Object.keys(levelNodes).map(Number).sort((a, b) => a - b);

        if (levelKeys.length === 0) return;

        const minLevel = Math.min(...levelKeys);
        const maxLevel = Math.max(...levelKeys);

        // 计算总体布局尺寸
        const totalWidth = levelKeys.length * (nodeWidth + rankSep);
        const maxNodesInLevel = Math.max(...Object.values(levelNodes).map(nodes => nodes.length));
        const totalHeight = maxNodesInLevel * (nodeHeight + nodeSep);

        levelKeys.forEach((level, levelIndex) => {
            const nodesInLevel = levelNodes[level];
            const levelX = levelIndex * (nodeWidth + rankSep) + nodeWidth / 2;

            // 计算该层级的垂直居中起始位置
            const levelHeight = nodesInLevel.length * nodeHeight + (nodesInLevel.length - 1) * nodeSep;
            const startY = (totalHeight - levelHeight) / 2 + nodeHeight / 2;

            nodesInLevel.forEach((nodeId, nodeIndex) => {
                const nodeY = startY + nodeIndex * (nodeHeight + nodeSep);

                graph.setNode(nodeId, {
                    ...graph.node(nodeId),
                    x: levelX,
                    y: nodeY,
                    width: nodeWidth,
                    height: nodeHeight
                });
            });
        });

        // 应用边交叉最小化调整
        this.minimizeEdgeCrossings(levelNodes, graph, levelKeys);
    }

    minimizeEdgeCrossings(levelNodes, graph, levelKeys) {
        // 简化的边交叉最小化算法
        for (let i = 0; i < 3; i++) { // 迭代3次
            levelKeys.forEach((level, levelIndex) => {
                if (levelIndex === 0) return;

                const currentLevelNodes = levelNodes[level];
                const prevLevel = levelKeys[levelIndex - 1];
                const prevLevelNodes = levelNodes[prevLevel];

                // 基于前一层的连接关系重新排序当前层
                const reorderedNodes = this.reorderByConnections(currentLevelNodes, prevLevelNodes, graph);

                // 重新分配Y坐标
                const nodeHeight = 40;
                const nodeSep = 50;
                const totalHeight = Object.values(levelNodes).reduce((max, nodes) => Math.max(max, nodes.length), 0) * (nodeHeight + nodeSep);
                const levelHeight = reorderedNodes.length * nodeHeight + (reorderedNodes.length - 1) * nodeSep;
                const startY = (totalHeight - levelHeight) / 2 + nodeHeight / 2;

                reorderedNodes.forEach((nodeId, nodeIndex) => {
                    const nodeData = graph.node(nodeId);
                    const nodeY = startY + nodeIndex * (nodeHeight + nodeSep);

                    graph.setNode(nodeId, {
                        ...nodeData,
                        y: nodeY
                    });
                });

                levelNodes[level] = reorderedNodes;
            });
        }
    }

    reorderByConnections(currentNodes, prevNodes, graph) {
        if (currentNodes.length <= 1) return currentNodes;

        // 计算每个当前节点与前一层节点的连接"重心"
        const nodePositions = {};

        currentNodes.forEach(nodeId => {
            let totalWeight = 0;
            let weightedSum = 0;

            prevNodes.forEach((prevNodeId, prevIndex) => {
                // 检查是否有连接
                const hasConnection = graph.edges().some(edge =>
                    (edge.v === prevNodeId && edge.w === nodeId) ||
                    (edge.v === nodeId && edge.w === prevNodeId)
                );

                if (hasConnection) {
                    totalWeight += 1;
                    weightedSum += prevIndex;
                }
            });

            nodePositions[nodeId] = totalWeight > 0 ? weightedSum / totalWeight : currentNodes.indexOf(nodeId);
        });

        // 按照连接重心排序
        return currentNodes.sort((a, b) => nodePositions[a] - nodePositions[b]);
    }

    calculateLevels(nodes, edges, graph) {
        const levels = {};
        const inDegree = {};
        const adjList = {};

        // 初始化
        nodes.forEach(nodeId => {
            inDegree[nodeId] = 0;
            adjList[nodeId] = [];
        });

        // 构建图
        edges.forEach(edge => {
            const sourceId = edge.v;
            const targetId = edge.w;
            adjList[sourceId].push(targetId);
            inDegree[targetId]++;
        });

        // 拓扑排序计算层级
        const queue = [];
        nodes.forEach(nodeId => {
            if (inDegree[nodeId] === 0) {
                queue.push({ id: nodeId, level: 0 });
                levels[nodeId] = 0;
            }
        });

        while (queue.length > 0) {
            const current = queue.shift();

            adjList[current.id].forEach(neighborId => {
                inDegree[neighborId]--;
                if (inDegree[neighborId] === 0) {
                    const newLevel = current.level + 1;
                    levels[neighborId] = newLevel;
                    queue.push({ id: neighborId, level: newLevel });
                }
            });
        }

        return levels;
    }
}

class SimpleGraph {
    constructor() {
        this._nodes = {};
        this._edges = [];
        this._graph = {};
    }

    setGraph(config) {
        this._graph = config;
    }

    graph() {
        return this._graph;
    }

    setDefaultNodeLabel(fn) {
        this._defaultNodeLabel = fn;
    }

    setDefaultEdgeLabel(fn) {
        this._defaultEdgeLabel = fn;
    }

    setNode(nodeId, label) {
        this._nodes[nodeId] = label || (this._defaultNodeLabel ? this._defaultNodeLabel() : {});
    }

    node(nodeId) {
        return this._nodes[nodeId];
    }

    hasNode(nodeId) {
        return nodeId in this._nodes;
    }

    setEdge(source, target, label) {
        this._edges.push({
            v: source,
            w: target,
            label: label || (this._defaultEdgeLabel ? this._defaultEdgeLabel() : {})
        });
    }

    nodes() {
        return Object.keys(this._nodes);
    }

    edges() {
        return this._edges;
    }
}

// 替代原来的 loadDagre 函数
async function loadDagre() {
    if (window.dagre) return window.dagre;

    // 返回我们的简化实现
    window.dagre = new SimpleGraphLayout();
    return window.dagre;
}

function transformLineage(data) {
    const allNodesMap = {};

    // Correctly collect all unique nodes from crtNode, backwardAtlas, and forwardAtlas
    if (data.crtNode) {
        allNodesMap[data.crtNode.resourceId] = data.crtNode;
    }
    (data.backwardAtlas?.nodes || []).forEach(node => {
        if (!allNodesMap[node.resourceId]) {
            allNodesMap[node.resourceId] = node;
        }
    });
    (data.forwardAtlas?.nodes || []).forEach(node => {
        if (!allNodesMap[node.resourceId]) {
            allNodesMap[node.resourceId] = node;
        }
    });

    const etlNodesList = Object.values(allNodesMap).filter(
        node => node.uniformResourceType === 'DATA_PROCESS_ETL'
    );

    const etlNodesMap = etlNodesList.reduce((map, node) => {
        map[node.resourceId] = node;
        return map;
    }, {});

    // 2. Helper function for graph traversal
    //    Finds ETL nodes reachable from startNodeId by traversing through non-ETL nodes.
    function findReachableEtlNodes(startNodeId, originalLinks, direction) {
        const reachableEtlTargets = new Set();
        // Queue stores { current node ID, path taken so far to detect cycles }
        const q = [{ currentId: startNodeId, path: new Set([startNodeId]) }];

        while (q.length > 0) {
            const { currentId, path } = q.shift(); // Using BFS

            let neighbors = [];
            if (direction === 'forward') {
                neighbors = (originalLinks || [])
                    .filter(link => link.sourceId === currentId)
                    .map(link => link.targetId);
            } else { // backward
                neighbors = (originalLinks || [])
                    .filter(link => link.targetId === currentId)
                    .map(link => link.sourceId);
            }

            for (const neighborId of neighbors) {
                if (path.has(neighborId)) { // Cycle detected in current traversal path
                    continue;
                }

                // Use the comprehensive allNodesMap for lookups during traversal
                const neighborNode = allNodesMap[neighborId];
                if (!neighborNode) {
                    // This might indicate inconsistent data, but we'll skip for robustness
                    continue;
                }

                if (neighborNode.uniformResourceType === 'DATA_PROCESS_ETL') {
                    reachableEtlTargets.add(neighborId);
                } else {
                    // Intermediate node, continue traversal
                    const newPath = new Set(path);
                    newPath.add(neighborId);
                    q.push({ currentId: neighborId, path: newPath });
                }
            }
        }
        return Array.from(reachableEtlTargets);
    }

    // 3. Build new forward links
    const newForwardLinks = [];
    const addedForwardLinksCache = new Set(); // Prevents duplicate source->target links
    for (const etlNode of etlNodesList) {
        const sourceId = etlNode.resourceId;
        const targetEtlIds = findReachableEtlNodes(sourceId, data.forwardAtlas?.links || [], 'forward');
        for (const targetId of targetEtlIds) {
            const linkKey = `${sourceId}->${targetId}`;
            if (!addedForwardLinksCache.has(linkKey)) {
                 newForwardLinks.push({
                    sourceId,
                    targetId,
                    sourceType: 'DATA_PROCESS_ETL',
                    targetType: 'DATA_PROCESS_ETL',
                    relativeDistance: 1, // Simplified for direct ETL-to-ETL link
                    subLinks: []
                });
                addedForwardLinksCache.add(linkKey);
            }
        }
    }

    // 4. Build new backward links
    const newBackwardLinks = [];
    const addedBackwardLinksCache = new Set(); // Prevents duplicate source->target links
    for (const etlNode of etlNodesList) {
        const targetId = etlNode.resourceId;
        // For backward links, the current etlNode is the target. We find its ETL sources.
        const sourceEtlIds = findReachableEtlNodes(targetId, data.backwardAtlas?.links || [], 'backward');
        for (const sourceId of sourceEtlIds) {
             const linkKey = `${sourceId}->${targetId}`;
             if (!addedBackwardLinksCache.has(linkKey)) {
                newBackwardLinks.push({
                    sourceId, // This is the ETL node found by traversing backwards
                    targetId, // This is the current etlNode we started from
                    sourceType: 'DATA_PROCESS_ETL',
                    targetType: 'DATA_PROCESS_ETL',
                    relativeDistance: -1, // Simplified for direct ETL-to-ETL link
                    subLinks: []
                });
                addedBackwardLinksCache.add(linkKey);
            }
        }
    }

    // Determine nodes for backwardAtlas
    const backwardLinkNodeIds = new Set();
    newBackwardLinks.forEach(link => {
        backwardLinkNodeIds.add(link.sourceId);
        backwardLinkNodeIds.add(link.targetId);
    });
    const backwardAtlasNodes = etlNodesList.filter(node => backwardLinkNodeIds.has(node.resourceId));

    // Determine nodes for forwardAtlas
    const forwardLinkNodeIds = new Set();
    newForwardLinks.forEach(link => {
        forwardLinkNodeIds.add(link.sourceId);
        forwardLinkNodeIds.add(link.targetId);
    });
    const forwardAtlasNodes = etlNodesList.filter(node => forwardLinkNodeIds.has(node.resourceId));

    let newCrtNode = null;
    if (data.crtNode && data.crtNode.uniformResourceType === 'DATA_PROCESS_ETL') {
        // Ensure crtNode is part of the overall ETL nodes list, if it's to be used.
        if (etlNodesMap[data.crtNode.resourceId]) {
            newCrtNode = data.crtNode;
            // Ensure crtNode is included in specific atlas nodes if it participates in their links
            // or if it's the designated crtNode and there are no links (e.g. isolated crtNode)
            if (!backwardLinkNodeIds.has(newCrtNode.resourceId) && backwardAtlasNodes.length > 0) {
                // This case is tricky: if crtNode is ETL but has no backward links in the new graph
                // should it be in backwardAtlasNodes? Typically, an atlas lists nodes involved in its links.
                // If it has no links, it might only appear if the atlas is empty otherwise and it IS the crtNode.
                // For now, let's stick to nodes involved in links.
            }
            if (!forwardLinkNodeIds.has(newCrtNode.resourceId) && forwardAtlasNodes.length > 0) {
                // Similar consideration for forward atlas.
            }
        }
    }

    return {
        crtNode: newCrtNode,
        backwardAtlas: {
            nodes: backwardAtlasNodes,
            links: newBackwardLinks
        },
        forwardAtlas: {
            nodes: forwardAtlasNodes,
            links: newForwardLinks
        },
        nodes: etlNodesList // This remains the list of ALL ETL nodes in the transformed graph
    };
}

// --- New code for route listening and plugin initialization ---

/**
 * Placeholder function to initialize the lineage plugin.
 * This function will be called when the specific route is matched.
 * @param {string} directoryId - The directory ID from the route.
 * @param {string} resourceId - The resource ID from the route.
 * @param {object} routeParams - The full route parameters from gd-route-change.
 */
function initLineagePlugin(directoryId, resourceId, routeParams) {

    const addBatchRunButtonWhenReady = (retriesLeft = 50) => {
        if (retriesLeft <= 0) {
            console.warn('Max retries reached for batch run button. Target element or admin rights might be missing.');
            return;
        }

        // Check for admin role first
        let isAdmin = false;
        if (typeof GD !== 'undefined' && typeof GD.getUser === 'function') {
            const userInfo = GD.getUser();
            if (userInfo && userInfo.role && userInfo.role.length > 0 && userInfo.role[0] === 'admin') {
                isAdmin = true;
            } else {
                // No need to retry if user is not admin, button should not appear.
                return;
            }
        } else {
            console.warn('GD.getUser() not available. Cannot determine user role for batch run button.');
            // Potentially retry if GD might become available later, but for now, let's assume if GD isn't there, role check fails.
            // If GD is essential and might load late, this retry logic might need to be outside role check.
            // For now, if GD not present, assume not admin for safety.
             setTimeout(() => {
                addBatchRunButtonWhenReady(retriesLeft - 1);
            }, 200);
            return;
        }

        if (!isAdmin) {
            // This case should ideally be caught by the return inside the role check, but as a safeguard:
            return;
        }

        // Proceed to find target element and add button ONLY if user is admin
        const targetElement = document.querySelector('.fg.fg-edit.gd-color-desc') ||
                              document.querySelector('.fg.fg-more.gd-color-desc');

        if (targetElement && targetElement.parentElement) {
            if (document.getElementById('batchRunButton')) {
                return;
            }

            const batchRunButton = document.createElement('button');
            batchRunButton.id = 'batchRunButton';
            batchRunButton.textContent = '批量重跑';
            batchRunButton.style.marginRight = '10px';
            batchRunButton.style.padding = '5px 10px';
            batchRunButton.style.border = '1px solid var(--color-border-outline)';
            batchRunButton.style.borderRadius = '4px';
            batchRunButton.style.cursor = 'pointer';
            batchRunButton.style.backgroundColor = 'var(--color-background)';
            batchRunButton.style.color = 'var(--color-text-body-default)';

            targetElement.parentElement.insertBefore(batchRunButton, targetElement);

            batchRunButton.addEventListener('click', () => {
                openBatchRunModal(resourceId);
            });
        } else {
            setTimeout(() => {
                addBatchRunButtonWhenReady(retriesLeft - 1);
            }, 200);
        }
    };

    GD.on('gd-ready', () => {
        addBatchRunButtonWhenReady();
    })

    addBatchRunButtonWhenReady();
}

/**
 * Creates and displays the batch run modal, fetches and processes lineage data.
 * @param {string} resourceId - The resource ID for which to fetch lineage.
 */
async function openBatchRunModal(resourceId) {
    const existingModal = document.getElementById('batchRunModal');
    if (existingModal) {
        existingModal.remove();
    }

    const modalOverlay = document.createElement('div');
    modalOverlay.id = 'batchRunModal';
    modalOverlay.style.position = 'fixed';
    modalOverlay.style.top = '0';
    modalOverlay.style.left = '0';
    modalOverlay.style.width = '100vw';
    modalOverlay.style.height = '100vh';
    modalOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    modalOverlay.style.display = 'flex';
    modalOverlay.style.justifyContent = 'center';
    modalOverlay.style.alignItems = 'center';
    modalOverlay.style.zIndex = '10000';

    const modalContent = document.createElement('div');
    modalContent.style.width = '100%';
    modalContent.style.height = '100%';
    modalContent.style.backgroundColor = 'var(--color-background-alt)';
    modalContent.style.padding = '20px';
    modalContent.style.position = 'relative';
    modalContent.style.overflowY = 'auto';
    modalContent.id = 'batchRunModalContent'; // Added ID for easier content manipulation

    const closeButton = document.createElement('button');
    closeButton.textContent = '×';
    closeButton.style.position = 'absolute';
    closeButton.style.top = '10px';
    closeButton.style.right = '16px';
    closeButton.style.fontSize = '24px';
    closeButton.style.border = 'none';
    closeButton.style.background = 'transparent';
    closeButton.style.color = 'var(--color-text-body-default)';
    closeButton.style.cursor = 'pointer';
    closeButton.style.padding = '0';
    closeButton.style.lineHeight = '1';
    closeButton.onclick = () => {
        modalOverlay.remove();
    };

    const modalTitle = document.createElement('h2');
    modalTitle.textContent = '批量运行设置';
    modalTitle.style.marginTop = '0';
    modalTitle.style.marginBottom = '20px';

    modalContent.appendChild(closeButton);
    modalContent.appendChild(modalTitle);

    // Create a container for dynamic content (loading/error/data)
    const dynamicContentContainer = document.createElement('div');
    dynamicContentContainer.id = 'modalDynamicContent';
    modalContent.appendChild(dynamicContentContainer);
    dynamicContentContainer.style.width = '100%';
    dynamicContentContainer.style.height = 'calc(100% - 120px)';
    dynamicContentContainer.style.overflow = 'auto';

    // --- Create Footer for Action Buttons ---
    const modalFooter = document.createElement('div');
    modalFooter.id = 'batchRunModalFooter';
    modalFooter.style.padding = '15px 20px';
    modalFooter.style.borderTop = '1px solid var(--color-border-divide)';
    modalFooter.style.textAlign = 'right'; // Align buttons to the right
    modalFooter.style.marginTop = 'auto'; // Push footer to bottom if content is short
    modalFooter.style.backgroundColor = 'var(--color-background)';
    modalFooter.style.position = 'absolute';
    modalFooter.style.bottom = '0';
    modalFooter.style.left = '0';
    modalFooter.style.right = '0';
    modalFooter.style.zIndex = 9999;

    // "全选" (Select All) Button
    const selectAllButton = document.createElement('button');
    selectAllButton.id = 'selectAllNodesButton';
    selectAllButton.textContent = '全选';
    selectAllButton.style.marginRight = '10px';
    selectAllButton.style.padding = '8px 15px';
    selectAllButton.style.border = '1px solid var(--color-border-outline)';
    selectAllButton.style.borderRadius = '4px';
    selectAllButton.style.cursor = 'pointer';
    selectAllButton.style.color = 'var(--color-text-body-default)';
    selectAllButton.style.backgroundColor = 'var(--color-background)';

    selectAllButton.addEventListener('click', () => {
        const checkboxes = modalContent.querySelectorAll('.node-header input[type="checkbox"]');
        const hasSomeUnchecked = Array.from(checkboxes).some(cb => !cb.checked);
        if (hasSomeUnchecked) {
            checkboxes.forEach(cb => {
                cb.checked = true;
            });
        } else {
            checkboxes.forEach(cb => {
                cb.checked = false;
            });
        }
        // console.log('All nodes selected/deselected.'); // Removed this log as per request
    });

    // "重跑" (Rerun) Button
    const rerunButton = document.createElement('button');
    rerunButton.id = 'rerunSelectedNodesButton';
    rerunButton.textContent = '重跑';
    rerunButton.style.padding = '8px 15px';
    rerunButton.style.border = '1px solid var(--color-primary)';
    rerunButton.style.backgroundColor = 'var(--color-primary)';
    rerunButton.style.color = 'white';
    rerunButton.style.borderRadius = '4px';
    rerunButton.style.cursor = 'pointer';

    rerunButton.addEventListener('click', () => {
        const selectedNodes = [];
        const checkboxes = modalContent.querySelectorAll('.node-header input[type="checkbox"]:checked');
        checkboxes.forEach(cb => {
            // Assuming checkbox ID is `cb-${resourceId}`
            const nodeId = cb.id.replace('cb-', '');
            selectedNodes.push(nodeId);
        });

        if (selectedNodes.length > 0) {
            // Sort selected nodes by dependency order (topological sort)
            const sortedNodes = topologicalSort(selectedNodes, currentTransformedData);
            // Actual rerun logic would go here
            rerunButton.disabled = true;
            fetch(`${window.__GD_PUBLIC_PATH__}/tzbank-custom/etl/batch/execute`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    etl: resourceId,
                    executeEtlIds: sortedNodes, // Use sorted nodes instead of original order
                })
            }).then(res => {
                if (res.ok) {
                    window.GD.dispatch('event-SYS_FETCH_SUCCESS', {
                        msg: '重跑成功',
                        ntfType: 0,
                    });
                    modalOverlay.remove();
                } else {
                    window.GD.dispatch('event-SYS_FETCH_FAIL', {
                        msg: `重跑失败: ${res.statusText}`,
                        ntfType: 0,
                    });
                    modalOverlay.remove();
                }
            }).finally(() => {
                rerunButton.disabled = false;
            })
        } else {
            window.GD.dispatch('event-SYS_FETCH_FAIL', {
                msg: '请选择至少一个节点进行重跑',
                ntfType: 0,
            });
            modalOverlay.remove();
        }
    });

    modalFooter.appendChild(selectAllButton);
    modalFooter.appendChild(rerunButton);

    // Append dynamic content container and then footer to modal content
    modalContent.appendChild(dynamicContentContainer); // Graph rendered here
    modalContent.appendChild(modalFooter); // Footer with buttons at the bottom

    modalOverlay.appendChild(modalContent);
    document.body.appendChild(modalOverlay);

    // --- Fetching and processing data ---
    dynamicContentContainer.textContent = 'Loading lineage data...';
    // ... (rest of the try-catch block for fetching and rendering graph remains the same)
    try {
        const apiUrl = `${window.__GD_PUBLIC_PATH__}/api/resource-atlas/${resourceId}`;
        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ resourceTypeName: "DATA_PROCESS_ETL" })
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`API request failed with status ${response.status}: ${errorText}`);
        }

        const lineageData = await response.json();
        dynamicContentContainer.textContent = 'Data loaded successfully. Processing...';

        const transformedData = transformLineage(lineageData);

        // Step 4: Render the graph
        dynamicContentContainer.innerHTML = ''; // Clear loading message
        injectModalGraphStyles();

        // Store transformed data globally for topological sorting
        currentTransformedData = transformedData;

        await renderLineageGraphInModal(transformedData, dynamicContentContainer, resourceId);

    } catch (error) {
        console.error('Error fetching or processing lineage data:', error);
        dynamicContentContainer.innerHTML = `<p style="color: red;">Error loading lineage data: ${error.message}</p>`;
        // Ensure footer is still visible even on error
        if (!modalContent.contains(modalFooter)) {
             modalContent.appendChild(modalFooter);
        }
    }
}

function injectModalGraphStyles() {
    const styleId = 'lineage-modal-styles';
    if (document.getElementById(styleId)) return; // Avoid duplicate styles

    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
        .graph-display-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
            background-color: var(--color-background-alt);
            border-radius: 4px;
            overflow: auto;
        }
        .graph-container {
            display: flex;
            align-items: flex-start;
            justify-content: flex-start;
            padding: 20px;
            gap: 50px;
            min-height: 300px;
            box-sizing: border-box;
            position: relative;
            z-index: 1;
        }
        .level-column {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 30px;
            min-width: 220px;
        }
        .node {
            background-color: var(--color-background-alt);
            border: 1px solid var(--color-border-outline);
            border-radius: 8px;
            padding: 12px 16px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            min-width: 252px;
            min-height: 40px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            border-left: 4px solid var(--color-primary);
            z-index: 2;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        .node:hover {
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
            transform: translateY(-1px);
        }
        .node.current-node {
            border-left-color: #52c41a;
            background-color: #f6ffed;
        }
        .node.current-node::before {
            content: "当前ETL";
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #52c41a;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 10;
        }
        .node-header {
            display: flex;
            align-items: center;
            width: 100%;
            margin-bottom: 4px;
        }
        .node-header input[type="checkbox"] {
            margin-right: 8px;
        }
        .node-name {
            font-weight: 600;
            font-size: 14px;
            color: var(--color-text-body-default);
            flex-grow: 1;
            line-height: 1.4;
            max-height: calc(1.4em * 2);
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }
        .node.current-node .node-name {
            color: #343a3f;
        }
        .etl-badge {
            background-color: var(--color-background);
            color: var(--color-primary);
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 3px;
            margin-right: 6px;
            border: 1px solid var(--color-border-outline);
            font-weight: 500;
        }
        .branch {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            margin-left: 20px;
            gap: 20px;
        }
        #modal-svg-canvas {
            position: absolute;
            top: 0;
            left: 0;
            pointer-events: none;
        }
    `;
    document.head.appendChild(style);
}

function createNodeElement(nodeData, isCurrentNode) {
    const nodeDiv = document.createElement('div');
    nodeDiv.className = 'node';
    if (isCurrentNode) {
        nodeDiv.classList.add('current-node');
    }
    nodeDiv.id = `lineage-node-${nodeData.resourceId}`;

    const nodeHeader = document.createElement('div');
    nodeHeader.className = 'node-header';

    const checkbox = document.createElement('input');
    checkbox.type = 'checkbox';
    checkbox.id = `cb-${nodeData.resourceId}`;
    checkbox.checked = false;

    const label = document.createElement('label');
    label.htmlFor = `cb-${nodeData.resourceId}`;
    label.className = 'node-name';
    label.textContent = nodeData.name || 'Unnamed ETL';

    const etlBadge = document.createElement('span');
    etlBadge.className = 'etl-badge';
    etlBadge.textContent = 'ETL';

    nodeHeader.appendChild(checkbox);
    nodeHeader.appendChild(etlBadge);
    nodeHeader.appendChild(label);
    nodeDiv.appendChild(nodeHeader);
    return nodeDiv;
}

function calcPath (sourceX, sourceY, targetX, targetY) {
    if (!sourceX || !targetX) return 'M 0 0 T 0 0'

    // M => 移动至起始目标点
    const M = `M ${sourceX} ${sourceY}`
    // Q => 绘制二次贝塞尔曲线
    const Q = `Q ${sourceX + Math.max(30, Math.abs(targetX - sourceX) / 4)} ${sourceY} ${(targetX + sourceX) / 2} ${(targetY + sourceY) / 2}`
    // T => 绘制三次贝塞尔曲线
    const T = `T ${targetX} ${targetY}`

    if (Math.abs(targetY - sourceY) <= 4) return `${M} ${T}`

    return `${M} ${Q} ${T}`
}

// 使用 Dagre 计算节点布局
async function calculateDagreLayout(transformedData, centerNodeId) {
    try {
        // 确保 dagre 库已加载
        const dagre = await loadDagre();

        // 创建一个新的 dagre 图
        const g = new dagre.graphlib.Graph();

        // 设置图的默认属性
        g.setGraph({
            rankdir: 'LR',           // 从左到右布局 (Left to Right)
            align: 'UL',             // 对齐方式：上左对齐
            nodesep: 80,             // 同层节点间距
            ranksep: 80,            // 不同层级间距
            marginx: 20,             // X方向边距
            marginy: 20,             // Y方向边距
            acyclicer: 'greedy',     // 处理循环依赖的策略
            ranker: 'tight-tree'     // 排序算法
        });

        // 设置默认的节点和边属性
        g.setDefaultNodeLabel(() => ({}));
        g.setDefaultEdgeLabel(() => ({}));

        // 添加节点到 dagre 图中
        const allNodes = transformedData.nodes || [];
        allNodes.forEach(node => {
            g.setNode(node.resourceId, {
                width: 252,      // 节点宽度
                height: 40,      // 节点高度
                label: node.name || 'Unnamed ETL'
            });
        });

        // 添加边到 dagre 图中
        const allLinks = [
            ...(transformedData.backwardAtlas?.links || []),
            ...(transformedData.forwardAtlas?.links || [])
        ];

        allLinks.forEach(link => {
            // 确保源和目标节点都存在
            if (g.hasNode(link.sourceId) && g.hasNode(link.targetId)) {
                g.setEdge(link.sourceId, link.targetId);
            }
        });

        // 运行布局算法
        dagre.layout(g);

        // 提取布局结果
        const layout = {};
        g.nodes().forEach(nodeId => {
            const node = g.node(nodeId);
            layout[nodeId] = {
                x: node.x,
                y: node.y,
                width: node.width,
                height: node.height
            };
        });

        return layout;
    } catch (error) {
        console.warn('Dagre layout failed, falling back to original layout:', error);
        return null;
    }
}

function drawModalLine(fromNodeId, toNodeId, g, graphDisplayWrapper) {
    const fromNode = document.getElementById(fromNodeId);
    const toNode = document.getElementById(toNodeId);

    if (!fromNode || !toNode) {
        console.warn(`Cannot draw line: Node not found. From: ${fromNodeId}, To: ${toNodeId}`);
        return;
    }

    const wrapperRect = graphDisplayWrapper.getBoundingClientRect();
    const fromRect = fromNode.getBoundingClientRect();
    const toRect = toNode.getBoundingClientRect();

    // Calculate coordinates relative to the graphDisplayWrapper
    // This assumes graphDisplayWrapper is the positioned parent of the SVG canvas
    // and that its BoundingClientRect is stable relative to the scrollable modal content.

    const x1 = fromRect.right - wrapperRect.left;
    const y1 = fromRect.top + (fromRect.height / 2) - wrapperRect.top;
    const x2 = toRect.left - wrapperRect.left;
    const y2 = toRect.top + (toRect.height / 2) - wrapperRect.top;

    const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    path.setAttribute('d', calcPath(x1, y1, x2 - 15, y2));
    path.setAttribute('stroke', 'var(--color-border-outline)');
    path.setAttribute('stroke-width', '1');
    path.setAttribute('fill', 'none');
    path.setAttribute('marker-end', 'url(#modal-arrowhead)');
    g.appendChild(path);
}

function calculateNodeLevels(transformedData, centerNodeId) {
    const nodeLevels = {};
    const q = [];
    const allEtlNodes = transformedData.nodes || [];
    const allEtlNodesMap = allEtlNodes.reduce((map, node) => {
        map[node.resourceId] = node;
        return map;
    }, {});

    if (!allEtlNodesMap[centerNodeId]) {
        console.error('Center node for level calculation not found in ETL nodes list.');
        return {}; // Or handle error appropriately
    }

    nodeLevels[centerNodeId] = 0;
    q.push({ id: centerNodeId, level: 0 });

    let head = 0;
    while(head < q.length) {
        const current = q[head++];

        // Backward traversal (upstream)
        (transformedData.backwardAtlas?.links || []).forEach(link => {
            if (link.targetId === current.id) {
                if (allEtlNodesMap[link.sourceId] && !(link.sourceId in nodeLevels)) {
                    nodeLevels[link.sourceId] = current.level - 1;
                    q.push({ id: link.sourceId, level: current.level - 1 });
                }
            }
        });

        // Forward traversal (downstream)
        (transformedData.forwardAtlas?.links || []).forEach(link => {
            if (link.sourceId === current.id) {
                if (allEtlNodesMap[link.targetId] && !(link.targetId in nodeLevels)) {
                    nodeLevels[link.targetId] = current.level + 1;
                    q.push({ id: link.targetId, level: current.level + 1 });
                }
            }
        });
    }
    return nodeLevels;
}

// 新增函数：优化节点在同一层内的排序
function optimizeNodePositionsInLevels(transformedData, nodeLevels) {
    const levelGroups = {};

    // 按level分组节点
    Object.entries(nodeLevels).forEach(([nodeId, level]) => {
        if (!levelGroups[level]) levelGroups[level] = [];
        levelGroups[level].push(nodeId);
    });

    const allLinks = [...(transformedData.backwardAtlas?.links || []), ...(transformedData.forwardAtlas?.links || [])];

    // 为每个level优化节点顺序
    Object.keys(levelGroups).forEach(level => {
        const nodes = levelGroups[level];
        if (nodes.length <= 1) return;

        // 计算每个节点的"权重"（连接数和连接目标的影响）
        const nodeWeights = {};
        nodes.forEach(nodeId => {
            let weight = 0;
            // 计算入度权重
            const incomingLinks = allLinks.filter(link => link.targetId === nodeId);
            // 计算出度权重
            const outgoingLinks = allLinks.filter(link => link.sourceId === nodeId);

            // 根据连接的目标节点位置计算权重
            incomingLinks.forEach(link => {
                const sourceLevel = nodeLevels[link.sourceId];
                if (sourceLevel !== undefined) {
                    weight += (sourceLevel < parseInt(level)) ? 100 : 50;
                }
            });

            outgoingLinks.forEach(link => {
                const targetLevel = nodeLevels[link.targetId];
                if (targetLevel !== undefined) {
                    weight += (targetLevel > parseInt(level)) ? 100 : 50;
                }
            });

            nodeWeights[nodeId] = weight;
        });

        // 按权重排序
        levelGroups[level] = nodes.sort((a, b) => nodeWeights[b] - nodeWeights[a]);
    });

    return levelGroups;
}

// 修改renderLineageGraphInModal函数
async function renderLineageGraphInModal(transformedData, container, currentResourceIdFromRoute) {
    container.innerHTML = '';
    const displayWrapper = document.createElement('div');
    displayWrapper.className = 'graph-display-wrapper';

    // ... 现有的SVG创建代码 ...
    const svgCanvas = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svgCanvas.id = 'modal-svg-canvas';
    const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
    const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
    marker.id = 'modal-arrowhead';
    marker.setAttribute('markerWidth', '8');
    marker.setAttribute('markerHeight', '6');
    marker.setAttribute('refX', '0');
    marker.setAttribute('refY', '3');
    marker.setAttribute('orient', 'auto');
    const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
    polygon.setAttribute('points', '0 0, 8 3, 0 6');
    polygon.setAttribute('fill', '#ccc');
    marker.appendChild(polygon);
    defs.appendChild(marker);
    svgCanvas.appendChild(defs);
    displayWrapper.appendChild(svgCanvas);

    const graphContainer = document.createElement('div');
    graphContainer.className = 'graph-container';
    displayWrapper.appendChild(graphContainer);
    container.appendChild(displayWrapper);

    const nodeElements = {};

    // ... 现有的centerNodeId逻辑 ...
    let centerNodeId = null;
    if (transformedData.crtNode && transformedData.crtNode.uniformResourceType === 'DATA_PROCESS_ETL') {
        centerNodeId = transformedData.crtNode.resourceId;
    } else if (currentResourceIdFromRoute) {
        const routeNodeIsEtl = (transformedData.nodes || []).find(n => n.resourceId === currentResourceIdFromRoute && n.uniformResourceType === 'DATA_PROCESS_ETL');
        if (routeNodeIsEtl) centerNodeId = currentResourceIdFromRoute;
    }
    if (!centerNodeId) {
        const firstEtlNode = (transformedData.nodes || []).find(n => n.uniformResourceType === 'DATA_PROCESS_ETL');
        if (firstEtlNode) {
            centerNodeId = firstEtlNode.resourceId;
            console.warn('Using first ETL node as center due to missing or non-ETL crtNode.');
        } else {
            container.textContent = 'No suitable ETL center node found for lineage display.';
            return;
        }
    }

    // 尝试使用 Dagre 布局，如果失败则使用原有逻辑
    const dagreLayout = await calculateDagreLayout(transformedData, centerNodeId);

    if (dagreLayout) {
        // 使用 Dagre 布局
        // 计算画布尺寸
        let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
        Object.values(dagreLayout).forEach(pos => {
            minX = Math.min(minX, pos.x - pos.width / 2);
            minY = Math.min(minY, pos.y - pos.height / 2);
            maxX = Math.max(maxX, pos.x + pos.width / 2);
            maxY = Math.max(maxY, pos.y + pos.height / 2);
        });

        const padding = 40;
        const canvasWidth = maxX - minX + padding * 2;
        const canvasHeight = maxY - minY + padding * 2;
        const offsetX = -minX + padding;
        const offsetY = -minY + padding;

        // 设置 SVG 尺寸
        svgCanvas.style.width = canvasWidth + 'px';
        svgCanvas.style.height = canvasHeight + 'px';
        svgCanvas.setAttribute('viewBox', `0 0 ${canvasWidth} ${canvasHeight}`);

        // 更新图容器样式以适应绝对定位
        graphContainer.style.position = 'relative';
        graphContainer.style.width = canvasWidth + 'px';
        graphContainer.style.height = canvasHeight + 'px';

        // 创建节点元素（使用 Dagre 布局）
        (transformedData.nodes || []).forEach(nodeData => {
            if (dagreLayout[nodeData.resourceId]) {
                const pos = dagreLayout[nodeData.resourceId];
                const isCurrent = nodeData.resourceId === centerNodeId;

                const nodeDiv = createNodeElement(nodeData, isCurrent);
                nodeDiv.style.position = 'absolute';
                nodeDiv.style.left = (pos.x - pos.width / 2 + offsetX) + 'px';
                nodeDiv.style.top = (pos.y - pos.height / 2 + offsetY) + 'px';
                nodeDiv.style.width = pos.width + 'px';
                nodeDiv.style.minHeight = pos.height + 'px';

                graphContainer.appendChild(nodeDiv);
                nodeElements[nodeData.resourceId] = nodeDiv;
            }
        });

        // 绘制连线（使用 Dagre 布局坐标）
        setTimeout(() => {
            const allLinks = [...(transformedData.backwardAtlas?.links || []), ...(transformedData.forwardAtlas?.links || [])];
            const drawnLinks = new Set();
            const g = document.createElementNS('http://www.w3.org/2000/svg', 'g');
            svgCanvas.appendChild(g);

            allLinks.forEach(link => {
                const linkKey = `${link.sourceId}->${link.targetId}`;
                if (nodeElements[link.sourceId] && nodeElements[link.targetId] && !drawnLinks.has(linkKey)) {
                    const sourcePos = dagreLayout[link.sourceId];
                    const targetPos = dagreLayout[link.targetId];

                    if (sourcePos && targetPos) {
                        // 计算连线坐标
                        const x1 = sourcePos.x + sourcePos.width / 2 + offsetX;
                        const y1 = sourcePos.y + offsetY;
                        const x2 = targetPos.x - targetPos.width / 2 + offsetX;
                        const y2 = targetPos.y + offsetY;

                        // 创建路径
                        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                        path.setAttribute('d', calcPath(x1, y1, x2 - 15, y2));
                        path.setAttribute('stroke', 'var(--color-border-outline)');
                        path.setAttribute('stroke-width', '2');
                        path.setAttribute('fill', 'none');
                        path.setAttribute('marker-end', 'url(#modal-arrowhead)');

                        g.appendChild(path);
                        drawnLinks.add(linkKey);
                    }
                }
            });
        }, 100);

    } else {
        // 使用原有的布局逻辑
        const nodeLevels = calculateNodeLevels(transformedData, centerNodeId);
        if (Object.keys(nodeLevels).length === 0) {
            container.textContent = 'Could not determine node levels for lineage display.';
            return;
        }

        // 新增：优化节点位置
        const optimizedLevelGroups = optimizeNodePositionsInLevels(transformedData, nodeLevels);

        const levels = Object.keys(optimizedLevelGroups).map(Number).sort((a, b) => a - b);
        const minLevel = Math.min(...levels);
        const maxLevel = Math.max(...levels);

        const columnDivs = {};
        for (let i = minLevel; i <= maxLevel; i++) {
            const columnDiv = document.createElement('div');
            columnDiv.className = 'level-column';
            columnDiv.style.display = 'flex';
            columnDiv.style.flexDirection = 'column';
            columnDiv.style.alignItems = 'center';
            columnDiv.style.gap = '20px';
            graphContainer.appendChild(columnDiv);
            columnDivs[i] = columnDiv;
        }

        // 按优化后的顺序添加节点
        levels.forEach(level => {
            const nodesInLevel = optimizedLevelGroups[level] || [];
            nodesInLevel.forEach(nodeId => {
                const nodeData = (transformedData.nodes || []).find(n => n.resourceId === nodeId);
                if (nodeData) {
                    const isCurrent = nodeData.resourceId === centerNodeId;
                    const htmlNode = createNodeElement(nodeData, isCurrent);
                    columnDivs[level].appendChild(htmlNode);
                    nodeElements[nodeData.resourceId] = htmlNode;
                }
            });
        });

        setTimeout(() => {
            if (displayWrapper.scrollWidth > 0 && displayWrapper.scrollHeight > 0) {
                svgCanvas.style.width = displayWrapper.scrollWidth + 'px';
                svgCanvas.style.height = displayWrapper.scrollHeight + 'px';
                svgCanvas.setAttribute('viewBox', `0 0 ${displayWrapper.scrollWidth} ${displayWrapper.scrollHeight}`);
            }

            const allLinks = [...(transformedData.backwardAtlas?.links || []), ...(transformedData.forwardAtlas?.links || [])];
            const drawnLinks = new Set();

            const g = document.createElementNS('http://www.w3.org/2000/svg', 'g');
            svgCanvas.appendChild(g);

            allLinks.forEach(link => {
                const linkKey = `${link.sourceId}->${link.targetId}`;
                if (nodeElements[link.sourceId] && nodeElements[link.targetId] && !drawnLinks.has(linkKey)) {
                    drawModalLine(nodeElements[link.sourceId].id, nodeElements[link.targetId].id, g, displayWrapper);
                    drawnLinks.add(linkKey);
                }
            });
        }, 200);
    }
}

/**
 * Handles the gd-route-change event from the GuanData SDK.
 * Checks if the current route matches the pattern for the data flow details page.
 * @param {object} params - The route parameters provided by the SDK.
 * @param {string} params.pathname - The path of the current URL.
 * @param {string} params.search - The query string of the current URL.
 * @param {string} params.hash - The hash fragment of the current URL.
 */
function handleRouteChange(params) {
    const { pathname } = params;
    // Regex to match /data-center/data-flow/{directoryId}/{resourceId}/details
    // Captures directoryId and resourceId
    const routePattern = /\/data-center\/data-flow\/([a-zA-Z0-9]+)\/([a-zA-Z0-9]+)\/details/;
    const match = pathname.match(routePattern);

    if (match) {
        const directoryId = match[1];
        const resourceId = match[2];
        initLineagePlugin(directoryId, resourceId, params);
    } else {
        // console.log(`Route did not match for lineage plugin: ${pathname}`);
    }
}

// Register the route change listener using the GuanData SDK.
// It's assumed that 'GD' object is available globally when this script runs.
// The SDK documentation mentions a 'gd-ready' event which might be relevant
// if GD.on is not immediately available, but typically event listeners can be set up early.
if (typeof GD !== 'undefined' && typeof GD.on === 'function') {
    GD.on('gd-route-change', handleRouteChange, 'LineagePluginDataFlowDetails');
} else {
    console.warn('GD SDK not found or GD.on is not a function. Lineage plugin route listener not attached.');
    // For development/testing outside the BI environment, you might want to manually trigger:
    // setTimeout(() => handleRouteChange({ pathname: '/data-center/data-flow/e2cb2c558bb8949eab3fd5de/h9807abc6324e4be7ab5d41a/details', search: '', hash: '' }), 1000);
}

/**
 * Performs topological sorting on the selected nodes based on DAG dependencies
 * Considers both direct and indirect dependencies through unselected intermediate nodes
 * @param {Array} selectedNodeIds - Array of selected node IDs
 * @param {Object} transformedData - The transformed lineage data containing links
 * @returns {Array} - Array of node IDs sorted in topological order (dependencies first)
 */
function topologicalSort(selectedNodeIds, transformedData) {
    if (selectedNodeIds.length <= 1) {
        return selectedNodeIds;
    }

    const selectedSet = new Set(selectedNodeIds);
    const allLinks = [
        ...(transformedData.backwardAtlas?.links || []),
        ...(transformedData.forwardAtlas?.links || [])
    ];

    // Build complete adjacency list (including all nodes, not just selected ones)
    const completeAdjList = {};
    const allNodes = new Set();

    allLinks.forEach(link => {
        const { sourceId, targetId } = link;
        allNodes.add(sourceId);
        allNodes.add(targetId);

        if (!completeAdjList[sourceId]) {
            completeAdjList[sourceId] = [];
        }
        completeAdjList[sourceId].push(targetId);
    });

    // Function to check if there's a path from source to target in the complete graph
    function hasPath(sourceId, targetId, visited = new Set()) {
        if (sourceId === targetId) {
            return true;
        }

        if (visited.has(sourceId)) {
            return false; // Cycle detection
        }

        visited.add(sourceId);

        const neighbors = completeAdjList[sourceId] || [];
        for (const neighbor of neighbors) {
            if (hasPath(neighbor, targetId, visited)) {
                return true;
            }
        }

        return false;
    }

    // Build simplified adjacency list and in-degree map for selected nodes only
    // but considering indirect dependencies through the complete graph
    const simplifiedAdjList = {};
    const inDegree = {};

    // Initialize
    selectedNodeIds.forEach(nodeId => {
        simplifiedAdjList[nodeId] = [];
        inDegree[nodeId] = 0;
    });

    // For each pair of selected nodes, check if there's a dependency path
    selectedNodeIds.forEach(sourceId => {
        selectedNodeIds.forEach(targetId => {
            if (sourceId !== targetId) {
                // Check if there's a path from sourceId to targetId in the complete graph
                if (hasPath(sourceId, targetId, new Set())) {
                    // Add edge in simplified graph
                    simplifiedAdjList[sourceId].push(targetId);
                    inDegree[targetId]++;
                }
            }
        });
    });

    // Kahn's algorithm for topological sorting on the simplified graph
    const queue = [];
    const result = [];

    // Find nodes with no incoming edges
    selectedNodeIds.forEach(nodeId => {
        if (inDegree[nodeId] === 0) {
            queue.push(nodeId);
        }
    });

    while (queue.length > 0) {
        const current = queue.shift();
        result.push(current);

        // Remove current node and update in-degrees
        simplifiedAdjList[current].forEach(neighbor => {
            inDegree[neighbor]--;
            if (inDegree[neighbor] === 0) {
                queue.push(neighbor);
            }
        });
    }

    // If result length doesn't match input, there might be cycles in selected subgraph
    // In that case, append remaining nodes to maintain all selected nodes
    if (result.length < selectedNodeIds.length) {
        const remaining = selectedNodeIds.filter(id => !result.includes(id));
        result.push(...remaining);
    }

    return result;
}
