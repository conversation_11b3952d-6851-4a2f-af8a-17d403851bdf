let currentTransformedData = null;

// 添加性能监控函数
function performanceMonitor(label, operation) {
    const start = performance.now();
    const result = operation();
    const end = performance.now();
    const duration = end - start;

    if (duration > 100) { // 只记录超过100ms的操作
        console.log(`Performance: ${label} took ${duration.toFixed(2)}ms`);
    }

    return result;
}

// 为异步操作提供的性能监控
async function performanceMonitorAsync(label, operation) {
    const start = performance.now();
    const result = await operation();
    const end = performance.now();
    const duration = end - start;

    if (duration > 100) {
        console.log(`Performance: ${label} took ${duration.toFixed(2)}ms`);
    }

    return result;
}

// 内置简化的图布局算法，替代 dagre 库
class SimpleGraphLayout {
    constructor() {
        this.graphlib = {
            Graph: function() {
                return new SimpleGraph();
            }
        };
    }

    layout(graph) {
        const nodes = graph.nodes();
        const edges = graph.edges();

        if (nodes.length === 0) return;

        // 计算每个节点的层级（基于拓扑排序）
        const levels = this.calculateLevels(nodes, edges, graph);

        // 为每个层级分配节点并优化排序
        const levelNodes = this.organizeLevels(levels, edges, graph);

        // 计算布局参数
        const config = graph.graph();
        const nodeWidth = 252;
        const nodeHeight = 40;
        const rankSep = config.ranksep || 100;
        const nodeSep = config.nodesep || 50;

        // 执行布局
        this.positionNodes(levelNodes, graph, nodeWidth, nodeHeight, rankSep, nodeSep);
    }

    organizeLevels(levels, edges, graph) {
        const levelNodes = {};

        // 按层级分组
        Object.entries(levels).forEach(([nodeId, level]) => {
            if (!levelNodes[level]) levelNodes[level] = [];
            levelNodes[level].push(nodeId);
        });

        // 对每个层级内的节点进行优化排序
        Object.keys(levelNodes).forEach(level => {
            levelNodes[level] = this.optimizeLevelOrder(levelNodes[level], edges, parseInt(level));
        });

        return levelNodes;
    }

    optimizeLevelOrder(nodesInLevel, edges, currentLevel) {
        if (nodesInLevel.length <= 1) return nodesInLevel;

        // 计算每个节点的"权重"以优化排序
        const nodeWeights = {};

        nodesInLevel.forEach(nodeId => {
            let weight = 0;
            let connectionCount = 0;

            // 计算上游连接的影响
            edges.forEach(edge => {
                if (edge.w === nodeId) {
                    weight += 100; // 入边权重
                    connectionCount++;
                }
                if (edge.v === nodeId) {
                    weight += 50;  // 出边权重
                    connectionCount++;
                }
            });

            // 连接数越多，权重越高
            nodeWeights[nodeId] = weight + connectionCount * 10;
        });

        // 按权重排序
        return nodesInLevel.sort((a, b) => nodeWeights[b] - nodeWeights[a]);
    }

    positionNodes(levelNodes, graph, nodeWidth, nodeHeight, rankSep, nodeSep) {
        const levelKeys = Object.keys(levelNodes).map(Number).sort((a, b) => a - b);

        if (levelKeys.length === 0) return;

        const minLevel = Math.min(...levelKeys);
        const maxLevel = Math.max(...levelKeys);

        // 动态计算间距以适应大量节点
        const totalNodes = Object.values(levelNodes).reduce((sum, nodes) => sum + nodes.length, 0);
        const maxNodesInLevel = Math.max(...Object.values(levelNodes).map(nodes => nodes.length));

        // 根据节点数量动态调整间距
        let dynamicNodeSep = nodeSep;
        let dynamicRankSep = rankSep;

        if (totalNodes > 100) {
            // 大量节点时减少间距
            dynamicNodeSep = Math.max(25, nodeSep - Math.floor(totalNodes / 50));
            dynamicRankSep = Math.max(60, rankSep - Math.floor(totalNodes / 100));
        }

        if (maxNodesInLevel > 30) {
            // 某层节点过多时进一步减少垂直间距
            dynamicNodeSep = Math.max(15, dynamicNodeSep - Math.floor(maxNodesInLevel / 10));
        }

        // 计算总体布局尺寸
        const totalWidth = levelKeys.length * (nodeWidth + dynamicRankSep);
        const totalHeight = maxNodesInLevel * (nodeHeight + dynamicNodeSep);

        levelKeys.forEach((level, levelIndex) => {
            const nodesInLevel = levelNodes[level];
            const levelX = levelIndex * (nodeWidth + dynamicRankSep) + nodeWidth / 2;

            // 改进的垂直分布算法
            const levelHeight = nodesInLevel.length * nodeHeight + (nodesInLevel.length - 1) * dynamicNodeSep;

            // 如果节点太多，使用更紧凑的布局
            let startY;
            if (nodesInLevel.length > 50) {
                // 超过50个节点时，使用更紧凑的布局
                const compactNodeSep = Math.max(10, dynamicNodeSep / 2);
                const compactLevelHeight = nodesInLevel.length * nodeHeight + (nodesInLevel.length - 1) * compactNodeSep;
                startY = Math.max(nodeHeight / 2, (totalHeight - compactLevelHeight) / 2 + nodeHeight / 2);

                nodesInLevel.forEach((nodeId, nodeIndex) => {
                    const nodeY = startY + nodeIndex * (nodeHeight + compactNodeSep);

                    graph.setNode(nodeId, {
                        ...graph.node(nodeId),
                        x: levelX,
                        y: nodeY,
                        width: nodeWidth,
                        height: nodeHeight
                    });
                });
            } else {
                // 正常布局
                startY = Math.max(nodeHeight / 2, (totalHeight - levelHeight) / 2 + nodeHeight / 2);

                nodesInLevel.forEach((nodeId, nodeIndex) => {
                    const nodeY = startY + nodeIndex * (nodeHeight + dynamicNodeSep);

                    graph.setNode(nodeId, {
                        ...graph.node(nodeId),
                        x: levelX,
                        y: nodeY,
                        width: nodeWidth,
                        height: nodeHeight
                    });
                });
            }
        });

        // 只在节点数量适中时进行边交叉最小化（性能优化）
        if (totalNodes <= 150) {
            this.minimizeEdgeCrossings(levelNodes, graph, levelKeys);
        } else {
            console.log(`Skipping edge crossing minimization for ${totalNodes} nodes to improve performance`);
        }
    }

    minimizeEdgeCrossings(levelNodes, graph, levelKeys) {
        // 简化的边交叉最小化算法
        for (let i = 0; i < 3; i++) { // 迭代3次
            levelKeys.forEach((level, levelIndex) => {
                if (levelIndex === 0) return;

                const currentLevelNodes = levelNodes[level];
                const prevLevel = levelKeys[levelIndex - 1];
                const prevLevelNodes = levelNodes[prevLevel];

                // 基于前一层的连接关系重新排序当前层
                const reorderedNodes = this.reorderByConnections(currentLevelNodes, prevLevelNodes, graph);

                // 重新分配Y坐标
                const nodeHeight = 40;
                const nodeSep = 50;
                const totalHeight = Object.values(levelNodes).reduce((max, nodes) => Math.max(max, nodes.length), 0) * (nodeHeight + nodeSep);
                const levelHeight = reorderedNodes.length * nodeHeight + (reorderedNodes.length - 1) * nodeSep;
                const startY = (totalHeight - levelHeight) / 2 + nodeHeight / 2;

                reorderedNodes.forEach((nodeId, nodeIndex) => {
                    const nodeData = graph.node(nodeId);
                    const nodeY = startY + nodeIndex * (nodeHeight + nodeSep);

                    graph.setNode(nodeId, {
                        ...nodeData,
                        y: nodeY
                    });
                });

                levelNodes[level] = reorderedNodes;
            });
        }
    }

    reorderByConnections(currentNodes, prevNodes, graph) {
        if (currentNodes.length <= 1) return currentNodes;

        // 计算每个当前节点与前一层节点的连接"重心"
        const nodePositions = {};

        currentNodes.forEach(nodeId => {
            let totalWeight = 0;
            let weightedSum = 0;

            prevNodes.forEach((prevNodeId, prevIndex) => {
                // 检查是否有连接
                const hasConnection = graph.edges().some(edge =>
                    (edge.v === prevNodeId && edge.w === nodeId) ||
                    (edge.v === nodeId && edge.w === prevNodeId)
                );

                if (hasConnection) {
                    totalWeight += 1;
                    weightedSum += prevIndex;
                }
            });

            nodePositions[nodeId] = totalWeight > 0 ? weightedSum / totalWeight : currentNodes.indexOf(nodeId);
        });

        // 按照连接重心排序
        return currentNodes.sort((a, b) => nodePositions[a] - nodePositions[b]);
    }

    calculateLevels(nodes, edges, graph) {
        const levels = {};
        const inDegree = {};
        const adjList = {};

        // 初始化
        nodes.forEach(nodeId => {
            inDegree[nodeId] = 0;
            adjList[nodeId] = [];
        });

        // 构建图
        edges.forEach(edge => {
            const sourceId = edge.v;
            const targetId = edge.w;
            adjList[sourceId].push(targetId);
            inDegree[targetId]++;
        });

        // 拓扑排序计算层级
        const queue = [];
        nodes.forEach(nodeId => {
            if (inDegree[nodeId] === 0) {
                queue.push({ id: nodeId, level: 0 });
                levels[nodeId] = 0;
            }
        });

        while (queue.length > 0) {
            const current = queue.shift();

            adjList[current.id].forEach(neighborId => {
                inDegree[neighborId]--;
                if (inDegree[neighborId] === 0) {
                    const newLevel = current.level + 1;
                    levels[neighborId] = newLevel;
                    queue.push({ id: neighborId, level: newLevel });
                }
            });
        }

        return levels;
    }
}

class SimpleGraph {
    constructor() {
        this._nodes = {};
        this._edges = [];
        this._graph = {};
    }

    setGraph(config) {
        this._graph = config;
    }

    graph() {
        return this._graph;
    }

    setDefaultNodeLabel(fn) {
        this._defaultNodeLabel = fn;
    }

    setDefaultEdgeLabel(fn) {
        this._defaultEdgeLabel = fn;
    }

    setNode(nodeId, label) {
        this._nodes[nodeId] = label || (this._defaultNodeLabel ? this._defaultNodeLabel() : {});
    }

    node(nodeId) {
        return this._nodes[nodeId];
    }

    hasNode(nodeId) {
        return nodeId in this._nodes;
    }

    setEdge(source, target, label) {
        this._edges.push({
            v: source,
            w: target,
            label: label || (this._defaultEdgeLabel ? this._defaultEdgeLabel() : {})
        });
    }

    nodes() {
        return Object.keys(this._nodes);
    }

    edges() {
        return this._edges;
    }
}

// 替代原来的 loadDagre 函数
async function loadDagre() {
    if (window.dagre) return window.dagre;

    // 返回我们的简化实现
    window.dagre = new SimpleGraphLayout();
    return window.dagre;
}

function transformLineage(data) {
    const allNodesMap = {};

    // Correctly collect all unique nodes from crtNode, backwardAtlas, and forwardAtlas
    if (data.crtNode) {
        allNodesMap[data.crtNode.resourceId] = data.crtNode;
    }
    (data.backwardAtlas?.nodes || []).forEach(node => {
        if (!allNodesMap[node.resourceId]) {
            allNodesMap[node.resourceId] = node;
        }
    });
    (data.forwardAtlas?.nodes || []).forEach(node => {
        if (!allNodesMap[node.resourceId]) {
            allNodesMap[node.resourceId] = node;
        }
    });

    const etlNodesList = Object.values(allNodesMap).filter(
        node => node.uniformResourceType === 'DATA_PROCESS_ETL'
    );

    const etlNodesMap = etlNodesList.reduce((map, node) => {
        map[node.resourceId] = node;
        return map;
    }, {});

    // 2. Helper function for graph traversal
    //    Finds ETL nodes reachable from startNodeId by traversing through non-ETL nodes.
    function findReachableEtlNodes(startNodeId, originalLinks, direction) {
        const reachableEtlTargets = new Set();
        // Queue stores { current node ID, path taken so far to detect cycles }
        const q = [{ currentId: startNodeId, path: new Set([startNodeId]) }];

        while (q.length > 0) {
            const { currentId, path } = q.shift(); // Using BFS

            let neighbors = [];
            if (direction === 'forward') {
                neighbors = (originalLinks || [])
                    .filter(link => link.sourceId === currentId)
                    .map(link => link.targetId);
            } else { // backward
                neighbors = (originalLinks || [])
                    .filter(link => link.targetId === currentId)
                    .map(link => link.sourceId);
            }

            for (const neighborId of neighbors) {
                if (path.has(neighborId)) { // Cycle detected in current traversal path
                    continue;
                }

                // Use the comprehensive allNodesMap for lookups during traversal
                const neighborNode = allNodesMap[neighborId];
                if (!neighborNode) {
                    // This might indicate inconsistent data, but we'll skip for robustness
                    continue;
                }

                if (neighborNode.uniformResourceType === 'DATA_PROCESS_ETL') {
                    reachableEtlTargets.add(neighborId);
                } else {
                    // Intermediate node, continue traversal
                    const newPath = new Set(path);
                    newPath.add(neighborId);
                    q.push({ currentId: neighborId, path: newPath });
                }
            }
        }
        return Array.from(reachableEtlTargets);
    }

    // 3. Build new forward links
    const newForwardLinks = [];
    const addedForwardLinksCache = new Set(); // Prevents duplicate source->target links
    for (const etlNode of etlNodesList) {
        const sourceId = etlNode.resourceId;
        const targetEtlIds = findReachableEtlNodes(sourceId, data.forwardAtlas?.links || [], 'forward');
        for (const targetId of targetEtlIds) {
            const linkKey = `${sourceId}->${targetId}`;
            if (!addedForwardLinksCache.has(linkKey)) {
                 newForwardLinks.push({
                    sourceId,
                    targetId,
                    sourceType: 'DATA_PROCESS_ETL',
                    targetType: 'DATA_PROCESS_ETL',
                    relativeDistance: 1, // Simplified for direct ETL-to-ETL link
                    subLinks: []
                });
                addedForwardLinksCache.add(linkKey);
            }
        }
    }

    // 4. Build new backward links
    const newBackwardLinks = [];
    const addedBackwardLinksCache = new Set(); // Prevents duplicate source->target links
    for (const etlNode of etlNodesList) {
        const targetId = etlNode.resourceId;
        // For backward links, the current etlNode is the target. We find its ETL sources.
        const sourceEtlIds = findReachableEtlNodes(targetId, data.backwardAtlas?.links || [], 'backward');
        for (const sourceId of sourceEtlIds) {
             const linkKey = `${sourceId}->${targetId}`;
             if (!addedBackwardLinksCache.has(linkKey)) {
                newBackwardLinks.push({
                    sourceId, // This is the ETL node found by traversing backwards
                    targetId, // This is the current etlNode we started from
                    sourceType: 'DATA_PROCESS_ETL',
                    targetType: 'DATA_PROCESS_ETL',
                    relativeDistance: -1, // Simplified for direct ETL-to-ETL link
                    subLinks: []
                });
                addedBackwardLinksCache.add(linkKey);
            }
        }
    }

    // 获取当前节点ID
    const currentNodeId = data.crtNode?.resourceId;

    if (!currentNodeId || !etlNodesMap[currentNodeId]) {
        // 如果没有当前节点或当前节点不是ETL节点，返回所有节点
        const backwardLinkNodeIds = new Set();
        newBackwardLinks.forEach(link => {
            backwardLinkNodeIds.add(link.sourceId);
            backwardLinkNodeIds.add(link.targetId);
        });
        const backwardAtlasNodes = etlNodesList.filter(node => backwardLinkNodeIds.has(node.resourceId));

        const forwardLinkNodeIds = new Set();
        newForwardLinks.forEach(link => {
            forwardLinkNodeIds.add(link.sourceId);
            forwardLinkNodeIds.add(link.targetId);
        });
        const forwardAtlasNodes = etlNodesList.filter(node => forwardLinkNodeIds.has(node.resourceId));

        return {
            crtNode: data.crtNode,
            backwardAtlas: {
                nodes: backwardAtlasNodes,
                links: newBackwardLinks
            },
            forwardAtlas: {
                nodes: forwardAtlasNodes,
                links: newForwardLinks
            },
            nodes: etlNodesList
        };
    }

    // 找到所有下游可达的ETL节点（包括当前节点）
    function findDownstreamEtlNodes(startNodeId, allForwardLinks) {
        const visited = new Set();
        const queue = [startNodeId];
        const downstreamNodes = new Set([startNodeId]); // 包含当前节点

        while (queue.length > 0) {
            const currentId = queue.shift();

            if (visited.has(currentId)) continue;
            visited.add(currentId);

            // 找到从当前节点出发的所有直接下游ETL节点
            allForwardLinks.forEach(link => {
                if (link.sourceId === currentId && !visited.has(link.targetId)) {
                    downstreamNodes.add(link.targetId);
                    queue.push(link.targetId);
                }
            });
        }

        return Array.from(downstreamNodes);
    }

    // 获取所有下游ETL节点ID（包括当前节点）
    const downstreamEtlNodeIds = findDownstreamEtlNodes(currentNodeId, newForwardLinks);
    const downstreamEtlNodesSet = new Set(downstreamEtlNodeIds);

    // 过滤节点：只保留下游ETL节点
    const filteredEtlNodes = etlNodesList.filter(node =>
        downstreamEtlNodesSet.has(node.resourceId)
    );

    // 过滤链接：只保留涉及下游ETL节点的链接
    const filteredForwardLinks = newForwardLinks.filter(link =>
        downstreamEtlNodesSet.has(link.sourceId) && downstreamEtlNodesSet.has(link.targetId)
    );

    let newCrtNode = null;
    if (data.crtNode && data.crtNode.uniformResourceType === 'DATA_PROCESS_ETL') {
        if (etlNodesMap[data.crtNode.resourceId]) {
            newCrtNode = data.crtNode;
        }
    }

    return {
        crtNode: newCrtNode,
        backwardAtlas: {
            nodes: [], // 不展示上游节点
            links: []  // 不展示上游链接
        },
        forwardAtlas: {
            nodes: filteredEtlNodes,
            links: filteredForwardLinks
        },
        nodes: filteredEtlNodes // 只包含当前节点和下游ETL节点
    };
}

// --- New code for route listening and plugin initialization ---

/**
 * Placeholder function to initialize the lineage plugin.
 * This function will be called when the specific route is matched.
 * @param {string} directoryId - The directory ID from the route.
 * @param {string} resourceId - The resource ID from the route.
 * @param {object} routeParams - The full route parameters from gd-route-change.
 */
function initLineagePlugin(directoryId, resourceId, routeParams) {

    const addBatchRunButtonWhenReady = (retriesLeft = 50) => {
        if (retriesLeft <= 0) {
            console.warn('Max retries reached for batch run button. Target element or admin rights might be missing.');
            return;
        }

        // Check for admin role first
        let isAdmin = false;
        if (typeof GD !== 'undefined' && typeof GD.getUser === 'function') {
            const userInfo = GD.getUser();
            if (userInfo && userInfo.role && userInfo.role.length > 0 && userInfo.role[0] === 'admin') {
                isAdmin = true;
            } else {
                // No need to retry if user is not admin, button should not appear.
                return;
            }
        } else {
            console.warn('GD.getUser() not available. Cannot determine user role for batch run button.');
            // Potentially retry if GD might become available later, but for now, let's assume if GD isn't there, role check fails.
            // If GD is essential and might load late, this retry logic might need to be outside role check.
            // For now, if GD not present, assume not admin for safety.
             setTimeout(() => {
                addBatchRunButtonWhenReady(retriesLeft - 1);
            }, 200);
            return;
        }

        if (!isAdmin) {
            // This case should ideally be caught by the return inside the role check, but as a safeguard:
            return;
        }

        // Proceed to find target element and add button ONLY if user is admin
        const targetElement = document.querySelector('.fg.fg-edit.gd-color-desc') ||
                              document.querySelector('.fg.fg-more.gd-color-desc');

        if (targetElement && targetElement.parentElement) {
            if (document.getElementById('batchRunButton')) {
                return;
            }

            const batchRunButton = document.createElement('button');
            batchRunButton.id = 'batchRunButton';
            batchRunButton.textContent = '批量重跑';
            batchRunButton.style.marginRight = '10px';
            batchRunButton.style.padding = '5px 10px';
            batchRunButton.style.border = '1px solid var(--color-border-outline)';
            batchRunButton.style.borderRadius = '4px';
            batchRunButton.style.cursor = 'pointer';
            batchRunButton.style.backgroundColor = 'var(--color-background)';
            batchRunButton.style.color = 'var(--color-text-body-default)';

            targetElement.parentElement.insertBefore(batchRunButton, targetElement);

            batchRunButton.addEventListener('click', () => {
                openBatchRunModal(resourceId);
            });
        } else {
            setTimeout(() => {
                addBatchRunButtonWhenReady(retriesLeft - 1);
            }, 200);
        }
    };

    GD.on('gd-ready', () => {
        addBatchRunButtonWhenReady();
    })

    addBatchRunButtonWhenReady();
}

/**
 * Creates and displays the batch run modal, fetches and processes lineage data.
 * @param {string} resourceId - The resource ID for which to fetch lineage.
 */
async function openBatchRunModal(resourceId) {
    const existingModal = document.getElementById('batchRunModal');
    if (existingModal) {
        existingModal.remove();
    }

    const modalOverlay = document.createElement('div');
    modalOverlay.id = 'batchRunModal';
    modalOverlay.style.position = 'fixed';
    modalOverlay.style.top = '0';
    modalOverlay.style.left = '0';
    modalOverlay.style.width = '100vw';
    modalOverlay.style.height = '100vh';
    modalOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.6)';
    modalOverlay.style.backdropFilter = 'blur(4px)';
    modalOverlay.style.display = 'flex';
    modalOverlay.style.justifyContent = 'center';
    modalOverlay.style.alignItems = 'center';
    modalOverlay.style.zIndex = '10000';
    modalOverlay.style.padding = '20px';

    const modalContent = document.createElement('div');
    modalContent.style.width = '90%';
    modalContent.style.height = '85vh';
    modalContent.style.maxHeight = '800px';
    modalContent.style.backgroundColor = 'var(--color-background-alt)';
    modalContent.style.borderRadius = '12px';
    modalContent.style.boxShadow = '0 20px 60px rgba(0, 0, 0, 0.3), 0 8px 32px rgba(0, 0, 0, 0.2)';
    modalContent.style.border = '1px solid var(--color-border-outline)';
    modalContent.style.padding = '0';
    modalContent.style.position = 'relative';
    modalContent.style.overflow = 'hidden';
    modalContent.style.display = 'flex';
    modalContent.style.flexDirection = 'column';
    modalContent.id = 'batchRunModalContent'; // Added ID for easier content manipulation

    const modalHeader = document.createElement('div');
    modalHeader.style.display = 'flex';
    modalHeader.style.justifyContent = 'space-between';
    modalHeader.style.alignItems = 'center';
    modalHeader.style.padding = '24px 24px 0 24px';
    modalHeader.style.borderBottom = '1px solid var(--color-border-divide)';
    modalHeader.style.marginBottom = '0';
    modalHeader.style.paddingBottom = '16px';
    modalHeader.style.flexShrink = '0';

    const modalTitle = document.createElement('h2');
    modalTitle.textContent = '批量运行设置';
    modalTitle.style.margin = '0';
    modalTitle.style.fontSize = '20px';
    modalTitle.style.fontWeight = '600';
    modalTitle.style.color = 'var(--color-text-body-default)';

    const closeButton = document.createElement('button');
    closeButton.textContent = '×';
    closeButton.style.width = '32px';
    closeButton.style.height = '32px';
    closeButton.style.borderRadius = '8px';
    closeButton.style.border = 'none';
    closeButton.style.background = 'var(--color-background-hover)';
    closeButton.style.color = 'var(--color-text-body-default)';
    closeButton.style.cursor = 'pointer';
    closeButton.style.fontSize = '18px';
    closeButton.style.fontWeight = 'bold';
    closeButton.style.display = 'flex';
    closeButton.style.alignItems = 'center';
    closeButton.style.justifyContent = 'center';
    closeButton.style.transition = 'all 0.2s ease';
    closeButton.onmouseover = () => {
        closeButton.style.background = 'var(--color-background-pressed)';
    };
    closeButton.onmouseout = () => {
        closeButton.style.background = 'var(--color-background-hover)';
    };
    closeButton.onclick = () => {
        modalOverlay.remove();
    };

    modalHeader.appendChild(modalTitle);
    modalHeader.appendChild(closeButton);
    modalContent.appendChild(modalHeader);

    // Create a container for dynamic content (loading/error/data)
    const dynamicContentContainer = document.createElement('div');
    dynamicContentContainer.id = 'modalDynamicContent';
    dynamicContentContainer.style.flex = '1';
    dynamicContentContainer.style.overflow = 'auto';
    modalContent.appendChild(dynamicContentContainer);

    // --- Create Footer for Action Buttons ---
    const modalFooter = document.createElement('div');
    modalFooter.id = 'batchRunModalFooter';
    modalFooter.style.padding = '16px 24px';
    modalFooter.style.borderTop = '1px solid var(--color-border-divide)';
    modalFooter.style.display = 'flex';
    modalFooter.style.justifyContent = 'flex-end';
    modalFooter.style.alignItems = 'center';
    modalFooter.style.gap = '12px';
    modalFooter.style.backgroundColor = 'var(--color-background-alt)';
    modalFooter.style.flexShrink = '0';

    // "取消" (Cancel) Button
    const cancelButton = document.createElement('button');
    cancelButton.id = 'cancelButton';
    cancelButton.textContent = '取消';
    cancelButton.style.padding = '10px 20px';
    cancelButton.style.border = '1px solid var(--color-border-outline)';
    cancelButton.style.borderRadius = '8px';
    cancelButton.style.cursor = 'pointer';
    cancelButton.style.color = 'var(--color-text-body-default)';
    cancelButton.style.backgroundColor = 'var(--color-background)';
    cancelButton.style.fontSize = '14px';
    cancelButton.style.fontWeight = '500';
    cancelButton.style.transition = 'all 0.2s ease';
    cancelButton.onmouseover = () => {
        cancelButton.style.backgroundColor = 'var(--color-background-hover)';
    };
    cancelButton.onmouseout = () => {
        cancelButton.style.backgroundColor = 'var(--color-background)';
    };
    cancelButton.onclick = () => {
        modalOverlay.remove();
    };

    // "全选" (Select All) Button
    const selectAllButton = document.createElement('button');
    selectAllButton.id = 'selectAllNodesButton';
    selectAllButton.textContent = '全选';
    selectAllButton.style.padding = '10px 20px';
    selectAllButton.style.border = '1px solid var(--color-border-outline)';
    selectAllButton.style.borderRadius = '8px';
    selectAllButton.style.cursor = 'pointer';
    selectAllButton.style.color = 'var(--color-text-body-default)';
    selectAllButton.style.backgroundColor = 'var(--color-background)';
    selectAllButton.style.fontSize = '14px';
    selectAllButton.style.fontWeight = '500';
    selectAllButton.style.transition = 'all 0.2s ease';
    selectAllButton.onmouseover = () => {
        selectAllButton.style.backgroundColor = 'var(--color-background-hover)';
        selectAllButton.style.borderColor = 'var(--color-primary)';
    };
    selectAllButton.onmouseout = () => {
        selectAllButton.style.backgroundColor = 'var(--color-background)';
        selectAllButton.style.borderColor = 'var(--color-border-outline)';
    };

    selectAllButton.addEventListener('click', () => {
        const checkboxes = modalContent.querySelectorAll('.node-header input[type="checkbox"]');
        const hasSomeUnchecked = Array.from(checkboxes).some(cb => !cb.checked);
        if (hasSomeUnchecked) {
            checkboxes.forEach(cb => {
                cb.checked = true;
            });
        } else {
            checkboxes.forEach(cb => {
                cb.checked = false;
            });
        }
        // console.log('All nodes selected/deselected.'); // Removed this log as per request
    });

    // "重跑" (Rerun) Button
    const rerunButton = document.createElement('button');
    rerunButton.id = 'rerunSelectedNodesButton';
    rerunButton.textContent = '重跑';
    rerunButton.style.padding = '10px 24px';
    rerunButton.style.border = '1px solid var(--color-primary)';
    rerunButton.style.backgroundColor = 'var(--color-primary)';
    rerunButton.style.color = 'white';
    rerunButton.style.borderRadius = '8px';
    rerunButton.style.cursor = 'pointer';
    rerunButton.style.fontSize = '14px';
    rerunButton.style.fontWeight = '600';
    rerunButton.style.transition = 'all 0.2s ease';
    rerunButton.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
    rerunButton.onmouseover = () => {
        rerunButton.style.transform = 'translateY(-1px)';
        rerunButton.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.15)';
    };
    rerunButton.onmouseout = () => {
        rerunButton.style.transform = 'translateY(0)';
        rerunButton.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
    };

    rerunButton.addEventListener('click', () => {
        const selectedNodes = [];
        const checkboxes = modalContent.querySelectorAll('.node-header input[type="checkbox"]:checked');
        checkboxes.forEach(cb => {
            // Assuming checkbox ID is `cb-${resourceId}`
            const nodeId = cb.id.replace('cb-', '');
            selectedNodes.push(nodeId);
        });

                  if (selectedNodes.length > 0) {
              // Sort selected nodes by dependency order (topological sort)
              const sortedNodes = performanceMonitor('topologicalSort', () =>
                  topologicalSort(currentTransformedData, selectedNodes)
              );
            // Actual rerun logic would go here
            rerunButton.disabled = true;
            fetch(`${window.__GD_PUBLIC_PATH__}/tzbank-custom/etl/batch/execute`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    etl: resourceId,
                    executeEtlIds: sortedNodes, // Use sorted nodes instead of original order
                })
            }).then(res => {
                if (res.ok) {
                    window.GD.dispatch('event-SYS_FETCH_SUCCESS', {
                        msg: '重跑成功',
                        ntfType: 0,
                    });
                    modalOverlay.remove();
                } else {
                    window.GD.dispatch('event-SYS_FETCH_FAIL', {
                        msg: `重跑失败: ${res.statusText}`,
                        ntfType: 0,
                    });
                    modalOverlay.remove();
                }
            }).finally(() => {
                rerunButton.disabled = false;
            })
        } else {
            window.GD.dispatch('event-SYS_FETCH_FAIL', {
                msg: '请选择至少一个节点进行重跑',
                ntfType: 0,
            });
            modalOverlay.remove();
        }
    });

    modalFooter.appendChild(cancelButton);
    modalFooter.appendChild(selectAllButton);
    modalFooter.appendChild(rerunButton);

    // Append dynamic content container and then footer to modal content
    modalContent.appendChild(dynamicContentContainer); // Graph rendered here
    modalContent.appendChild(modalFooter); // Footer with buttons at the bottom

    modalOverlay.appendChild(modalContent);
    document.body.appendChild(modalOverlay);

    // --- Fetching and processing data ---
    dynamicContentContainer.textContent = 'Loading lineage data...';
    // ... (rest of the try-catch block for fetching and rendering graph remains the same)
    try {
        const apiUrl = `${window.__GD_PUBLIC_PATH__}/api/resource-atlas/${resourceId}`;
        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ resourceTypeName: "DATA_PROCESS_ETL" })
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`API request failed with status ${response.status}: ${errorText}`);
        }

        const lineageData = await response.json();
        dynamicContentContainer.textContent = 'Data loaded successfully. Processing...';

        const transformedData = transformLineage(lineageData);

        // Step 4: Render the graph
        dynamicContentContainer.innerHTML = ''; // Clear loading message
        injectModalGraphStyles();

        // Store transformed data globally for topological sorting
        currentTransformedData = transformedData;

        await renderLineageGraphInModal(transformedData, dynamicContentContainer, resourceId);

    } catch (error) {
        console.error('Error fetching or processing lineage data:', error);
        dynamicContentContainer.innerHTML = `<p style="color: red;">Error loading lineage data: ${error.message}</p>`;
        // Ensure footer is still visible even on error
        if (!modalContent.contains(modalFooter)) {
             modalContent.appendChild(modalFooter);
        }
    }
}

function injectModalGraphStyles() {
    const styleId = 'lineage-modal-styles';
    if (document.getElementById(styleId)) return; // Avoid duplicate styles

    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
        .graph-display-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
            background-color: var(--color-background-alt);
            border-radius: 4px;
            overflow: auto;
        }
        .graph-container {
            display: flex;
            align-items: flex-start;
            justify-content: flex-start;
            padding: 20px;
            gap: 50px;
            min-height: 300px;
            box-sizing: border-box;
            position: relative;
            z-index: 1;
        }
        .level-column {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 30px;
            min-width: 220px;
        }
        .node {
            background-color: var(--color-background-alt);
            border: 1px solid var(--color-border-outline);
            border-radius: 8px;
            padding: 12px 16px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            min-width: 252px;
            min-height: 40px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            position: relative;
            border-left: 4px solid var(--color-primary);
            z-index: 2;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        .node:hover {
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
            transform: translateY(-1px);
        }
        .node.current-node {
            border-left-color: #52c41a;
            background-color: #f6ffed;
        }
        .node.current-node::before {
            content: "当前ETL";
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #52c41a;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 10;
        }
        .node-header {
            display: flex;
            align-items: center;
            width: 100%;
            margin-bottom: 4px;
        }
        .node-header input[type="checkbox"] {
            margin-right: 8px;
        }
        .node-name {
            font-weight: 600;
            font-size: 14px;
            color: var(--color-text-body-default);
            flex-grow: 1;
            line-height: 1.4;
            max-height: calc(1.4em * 2);
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }
        .node.current-node .node-name {
            color: #343a3f;
        }
        .etl-badge {
            background-color: var(--color-background);
            color: var(--color-primary);
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 3px;
            margin-right: 6px;
            border: 1px solid var(--color-border-outline);
            font-weight: 500;
        }
        .branch {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            margin-left: 20px;
            gap: 20px;
        }
        #modal-svg-canvas {
            position: absolute;
            top: 0;
            left: 0;
            pointer-events: none;
        }
    `;
    document.head.appendChild(style);
}

function createNodeElement(nodeData, isCurrentNode) {
    const nodeDiv = document.createElement('div');
    nodeDiv.className = 'node';
    if (isCurrentNode) {
        nodeDiv.classList.add('current-node');
    }
    nodeDiv.id = `lineage-node-${nodeData.resourceId}`;

    const nodeHeader = document.createElement('div');
    nodeHeader.className = 'node-header';

    const checkbox = document.createElement('input');
    checkbox.type = 'checkbox';
    checkbox.id = `cb-${nodeData.resourceId}`;
    checkbox.checked = false;

    const label = document.createElement('label');
    label.htmlFor = `cb-${nodeData.resourceId}`;
    label.className = 'node-name';
    label.textContent = nodeData.name || 'Unnamed ETL';

    const etlBadge = document.createElement('span');
    etlBadge.className = 'etl-badge';
    etlBadge.textContent = 'ETL';

    nodeHeader.appendChild(checkbox);
    nodeHeader.appendChild(etlBadge);
    nodeHeader.appendChild(label);
    nodeDiv.appendChild(nodeHeader);
    return nodeDiv;
}

function calcPath (sourceX, sourceY, targetX, targetY) {
    if (!sourceX || !targetX) return 'M 0 0 T 0 0'

    // M => 移动至起始目标点
    const M = `M ${sourceX} ${sourceY}`
    // Q => 绘制二次贝塞尔曲线
    const Q = `Q ${sourceX + Math.max(30, Math.abs(targetX - sourceX) / 4)} ${sourceY} ${(targetX + sourceX) / 2} ${(targetY + sourceY) / 2}`
    // T => 绘制三次贝塞尔曲线
    const T = `T ${targetX} ${targetY}`

    if (Math.abs(targetY - sourceY) <= 4) return `${M} ${T}`

    return `${M} ${Q} ${T}`
}

// 使用 Dagre 计算节点布局
async function calculateDagreLayout(transformedData, centerNodeId) {
    try {
        // 确保 dagre 库已加载
        const dagre = await loadDagre();

        // 创建一个新的 dagre 图
        const g = new dagre.graphlib.Graph();

        // 设置图的默认属性
        g.setGraph({
            rankdir: 'LR',           // 从左到右布局 (Left to Right)
            align: 'UL',             // 对齐方式：上左对齐
            nodesep: 80,             // 同层节点间距
            ranksep: 80,            // 不同层级间距
            marginx: 20,             // X方向边距
            marginy: 20,             // Y方向边距
            acyclicer: 'greedy',     // 处理循环依赖的策略
            ranker: 'tight-tree'     // 排序算法
        });

        // 设置默认的节点和边属性
        g.setDefaultNodeLabel(() => ({}));
        g.setDefaultEdgeLabel(() => ({}));

        // 添加节点到 dagre 图中
        const allNodes = transformedData.nodes || [];
        allNodes.forEach(node => {
            g.setNode(node.resourceId, {
                width: 252,      // 节点宽度
                height: 40,      // 节点高度
                label: node.name || 'Unnamed ETL'
            });
        });

        // 添加边到 dagre 图中
        const allLinks = [
            ...(transformedData.backwardAtlas?.links || []),
            ...(transformedData.forwardAtlas?.links || [])
        ];

        allLinks.forEach(link => {
            // 确保源和目标节点都存在
            if (g.hasNode(link.sourceId) && g.hasNode(link.targetId)) {
                g.setEdge(link.sourceId, link.targetId);
            }
        });

        // 运行布局算法
        dagre.layout(g);

        // 提取布局结果
        const layout = {};
        g.nodes().forEach(nodeId => {
            const node = g.node(nodeId);
            layout[nodeId] = {
                x: node.x,
                y: node.y,
                width: node.width,
                height: node.height
            };
        });

        return layout;
    } catch (error) {
        console.warn('Dagre layout failed, falling back to original layout:', error);
        return null;
    }
}

function drawModalLine(fromNodeId, toNodeId, g, graphDisplayWrapper) {
    const fromNode = document.getElementById(fromNodeId);
    const toNode = document.getElementById(toNodeId);

    if (!fromNode || !toNode) {
        console.warn(`Cannot draw line: Node not found. From: ${fromNodeId}, To: ${toNodeId}`);
        return;
    }

    const wrapperRect = graphDisplayWrapper.getBoundingClientRect();
    const fromRect = fromNode.getBoundingClientRect();
    const toRect = toNode.getBoundingClientRect();

    // Calculate coordinates relative to the graphDisplayWrapper
    // This assumes graphDisplayWrapper is the positioned parent of the SVG canvas
    // and that its BoundingClientRect is stable relative to the scrollable modal content.

    const x1 = fromRect.right - wrapperRect.left;
    const y1 = fromRect.top + (fromRect.height / 2) - wrapperRect.top;
    const x2 = toRect.left - wrapperRect.left;
    const y2 = toRect.top + (toRect.height / 2) - wrapperRect.top;

    const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    path.setAttribute('d', calcPath(x1, y1, x2 - 15, y2));
    path.setAttribute('stroke', 'var(--color-border-outline)');
    path.setAttribute('stroke-width', '1');
    path.setAttribute('fill', 'none');
    path.setAttribute('marker-end', 'url(#modal-arrowhead)');
    g.appendChild(path);
}

function calculateNodeLevels(transformedData, centerNodeId) {
    const nodeLevels = {};
    const allEtlNodes = transformedData.nodes || [];
    const allEtlNodesMap = allEtlNodes.reduce((map, node) => {
        map[node.resourceId] = node;
        return map;
    }, {});

    if (!allEtlNodesMap[centerNodeId]) {
        console.error('Center node for level calculation not found in ETL nodes list.');
        return {}; // Or handle error appropriately
    }

    const allLinks = transformedData.forwardAtlas?.links || [];

    // 构建邻接表
    const adjList = {};
    allEtlNodes.forEach(node => {
        adjList[node.resourceId] = [];
    });

    allLinks.forEach(link => {
        if (allEtlNodesMap[link.sourceId] && allEtlNodesMap[link.targetId]) {
            adjList[link.sourceId].push(link.targetId);
        }
    });

    // 使用BFS正确计算层级关系，不考虑节点数量限制
    nodeLevels[centerNodeId] = 0;
    const queue = [{ id: centerNodeId, level: 0 }];

    while (queue.length > 0) {
        const current = queue.shift();

        // 获取当前节点的所有直接下游邻居
        const neighbors = adjList[current.id] || [];

        for (const neighborId of neighbors) {
            // 只有未访问过的节点才分配层级
            if (!(neighborId in nodeLevels) && allEtlNodesMap[neighborId]) {
                const correctLevel = current.level + 1;
                nodeLevels[neighborId] = correctLevel;
                queue.push({ id: neighborId, level: correctLevel });
            }
        }
    }

    // 对于没有被访问到的节点（可能是孤立节点），分配到最后一层
    const unvisitedNodes = allEtlNodes.filter(node => !(node.resourceId in nodeLevels));
    if (unvisitedNodes.length > 0) {
        const maxLevel = Object.keys(nodeLevels).length > 0 ? Math.max(...Object.values(nodeLevels)) : 0;
        const isolatedLevel = maxLevel + 1;

        unvisitedNodes.forEach(node => {
            nodeLevels[node.resourceId] = isolatedLevel;
        });
    }

    return nodeLevels;
}

// 新增函数：优化节点在同一层内的排序
function optimizeNodePositionsInLevels(transformedData, nodeLevels) {
    const levelGroups = {};

    // 按level分组节点
    Object.entries(nodeLevels).forEach(([nodeId, level]) => {
        if (!levelGroups[level]) levelGroups[level] = [];
        levelGroups[level].push(nodeId);
    });

    // 只使用下游链接，因为现在只显示下游节点
    const allLinks = transformedData.forwardAtlas?.links || [];

    // 性能优化：预构建链接索引
    const linksBySource = {};
    const linksByTarget = {};

    allLinks.forEach(link => {
        if (!linksBySource[link.sourceId]) linksBySource[link.sourceId] = [];
        if (!linksByTarget[link.targetId]) linksByTarget[link.targetId] = [];
        linksBySource[link.sourceId].push(link);
        linksByTarget[link.targetId].push(link);
    });

    // 为每个level优化节点顺序
    Object.keys(levelGroups).forEach(level => {
        const nodes = levelGroups[level];
        if (nodes.length <= 1) return;

        // 性能优化：对于大量节点的层级，使用简化的排序策略
        if (nodes.length > 50) {
            // 简化策略：按连接数排序
            const nodeConnectionCounts = {};
            nodes.forEach(nodeId => {
                const incomingCount = (linksByTarget[nodeId] || []).length;
                const outgoingCount = (linksBySource[nodeId] || []).length;
                nodeConnectionCounts[nodeId] = incomingCount + outgoingCount;
            });

            levelGroups[level] = nodes.sort((a, b) => nodeConnectionCounts[b] - nodeConnectionCounts[a]);
            return;
        }

        // 完整策略：计算每个节点的"权重"（连接数和连接目标的影响）
        const nodeWeights = {};
        nodes.forEach(nodeId => {
            let weight = 0;

            // 计算入度权重（使用预构建的索引）
            const incomingLinks = linksByTarget[nodeId] || [];
            const outgoingLinks = linksBySource[nodeId] || [];

            // 根据连接的目标节点位置计算权重
            incomingLinks.forEach(link => {
                const sourceLevel = nodeLevels[link.sourceId];
                if (sourceLevel !== undefined) {
                    weight += (sourceLevel < parseInt(level)) ? 100 : 50;
                }
            });

            outgoingLinks.forEach(link => {
                const targetLevel = nodeLevels[link.targetId];
                if (targetLevel !== undefined) {
                    weight += (targetLevel > parseInt(level)) ? 100 : 50;
                }
            });

            nodeWeights[nodeId] = weight;
        });

        // 按权重排序
        levelGroups[level] = nodes.sort((a, b) => nodeWeights[b] - nodeWeights[a]);
    });

    return levelGroups;
}

// 修改renderLineageGraphInModal函数
async function renderLineageGraphInModal(transformedData, container, currentResourceIdFromRoute) {
    container.innerHTML = '';
    const displayWrapper = document.createElement('div');
    displayWrapper.className = 'graph-display-wrapper';

    // 性能检查：对于超大图进行预警
    const totalNodes = (transformedData.nodes || []).length;
    const totalLinks = (transformedData.forwardAtlas?.links || []).length;

    if (totalNodes > 500) {
        const warningDiv = document.createElement('div');
        warningDiv.style.padding = '20px';
        warningDiv.style.backgroundColor = '#fff3cd';
        warningDiv.style.border = '1px solid #ffeaa7';
        warningDiv.style.borderRadius = '4px';
        warningDiv.style.marginBottom = '20px';
        warningDiv.innerHTML = `
            <strong>⚠️ 性能提示：</strong> 检测到 ${totalNodes} 个节点。
            为了提高性能，将采用简化布局模式。
        `;
        container.appendChild(warningDiv);
    }

    // ... 现有的SVG创建代码 ...
    const svgCanvas = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svgCanvas.id = 'modal-svg-canvas';
    const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
    const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
    marker.id = 'modal-arrowhead';
    marker.setAttribute('markerWidth', '8');
    marker.setAttribute('markerHeight', '6');
    marker.setAttribute('refX', '0');
    marker.setAttribute('refY', '3');
    marker.setAttribute('orient', 'auto');
    const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
    polygon.setAttribute('points', '0 0, 8 3, 0 6');
    polygon.setAttribute('fill', '#ccc');
    marker.appendChild(polygon);
    defs.appendChild(marker);
    svgCanvas.appendChild(defs);
    displayWrapper.appendChild(svgCanvas);

    const graphContainer = document.createElement('div');
    graphContainer.className = 'graph-container';
    displayWrapper.appendChild(graphContainer);
    container.appendChild(displayWrapper);

    const nodeElements = {};

    // ... 现有的centerNodeId逻辑 ...
    let centerNodeId = null;
    if (transformedData.crtNode && transformedData.crtNode.uniformResourceType === 'DATA_PROCESS_ETL') {
        centerNodeId = transformedData.crtNode.resourceId;
    } else if (currentResourceIdFromRoute) {
        const routeNodeIsEtl = (transformedData.nodes || []).find(n => n.resourceId === currentResourceIdFromRoute && n.uniformResourceType === 'DATA_PROCESS_ETL');
        if (routeNodeIsEtl) centerNodeId = currentResourceIdFromRoute;
    }
    if (!centerNodeId) {
        const firstEtlNode = (transformedData.nodes || []).find(n => n.uniformResourceType === 'DATA_PROCESS_ETL');
        if (firstEtlNode) {
            centerNodeId = firstEtlNode.resourceId;
            console.warn('Using first ETL node as center due to missing or non-ETL crtNode.');
        } else {
            container.textContent = 'No suitable ETL center node found for lineage display.';
            return;
        }
    }

    // 性能优化：超过300个节点时强制使用简化布局
    const useSimplifiedLayout = totalNodes > 300;
    let dagreLayout = null;

    if (!useSimplifiedLayout) {
        // 尝试使用 Dagre 布局，如果失败则使用原有逻辑
        dagreLayout = await performanceMonitorAsync('calculateDagreLayout', () =>
            calculateDagreLayout(transformedData, centerNodeId)
        );
    }

    if (dagreLayout && !useSimplifiedLayout) {
        // 使用 Dagre 布局
        // 计算画布尺寸
        let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

        // 验证 dagreLayout 是否有有效的位置信息
        const validPositions = Object.values(dagreLayout).filter(pos =>
            pos && typeof pos.x === 'number' && !isNaN(pos.x) &&
            typeof pos.y === 'number' && !isNaN(pos.y) &&
            typeof pos.width === 'number' && !isNaN(pos.width) &&
            typeof pos.height === 'number' && !isNaN(pos.height)
        );

        if (validPositions.length === 0) {
            console.warn('No valid positions found in dagreLayout, falling back to simplified layout');
            dagreLayout = null; // 强制使用简化布局
        } else {
            validPositions.forEach(pos => {
                minX = Math.min(minX, pos.x - pos.width / 2);
                minY = Math.min(minY, pos.y - pos.height / 2);
                maxX = Math.max(maxX, pos.x + pos.width / 2);
                maxY = Math.max(maxY, pos.y + pos.height / 2);
            });
        }

        const padding = 40;
        let canvasWidth, canvasHeight, offsetX, offsetY;

        if (dagreLayout) {
            canvasWidth = maxX - minX + padding * 2;
            canvasHeight = maxY - minY + padding * 2;
            offsetX = -minX + padding;
            offsetY = -minY + padding;

            // 最后的安全检查
            if (isNaN(canvasWidth) || isNaN(canvasHeight)) {
                console.warn('Canvas dimensions are NaN, using default values');
                canvasWidth = 800;
                canvasHeight = 600;
                offsetX = padding;
                offsetY = padding;
            }
        }

        // 设置 SVG 尺寸
        if (dagreLayout) {
            svgCanvas.style.width = canvasWidth + 'px';
            svgCanvas.style.height = canvasHeight + 'px';
            svgCanvas.setAttribute('viewBox', `0 0 ${canvasWidth} ${canvasHeight}`);
        }

        // 更新图容器样式以适应绝对定位
        if (dagreLayout) {
            graphContainer.style.position = 'relative';
            graphContainer.style.width = canvasWidth + 'px';
            graphContainer.style.height = canvasHeight + 'px';
        }

        // 创建节点元素（使用 Dagre 布局）
        (transformedData.nodes || []).forEach(nodeData => {
            if (dagreLayout[nodeData.resourceId]) {
                const pos = dagreLayout[nodeData.resourceId];
                const isCurrent = nodeData.resourceId === centerNodeId;

                const nodeDiv = createNodeElement(nodeData, isCurrent);
                nodeDiv.style.position = 'absolute';
                nodeDiv.style.left = (pos.x - pos.width / 2 + offsetX) + 'px';
                nodeDiv.style.top = (pos.y - pos.height / 2 + offsetY) + 'px';
                nodeDiv.style.width = pos.width + 'px';
                nodeDiv.style.minHeight = pos.height + 'px';

                graphContainer.appendChild(nodeDiv);
                nodeElements[nodeData.resourceId] = nodeDiv;
            }
        });

        // 绘制连线（使用 Dagre 布局坐标）
        setTimeout(() => {
            // 只绘制下游链接
            const allLinks = transformedData.forwardAtlas?.links || [];
            const drawnLinks = new Set();
            const g = document.createElementNS('http://www.w3.org/2000/svg', 'g');
            svgCanvas.appendChild(g);

            // 性能优化：限制连线数量
            const maxLinesToDraw = Math.min(allLinks.length, 1000);
            const linksToProcess = totalNodes > 200 ? allLinks.slice(0, maxLinesToDraw) : allLinks;

            linksToProcess.forEach(link => {
                const linkKey = `${link.sourceId}->${link.targetId}`;
                if (nodeElements[link.sourceId] && nodeElements[link.targetId] && !drawnLinks.has(linkKey)) {
                    const sourcePos = dagreLayout[link.sourceId];
                    const targetPos = dagreLayout[link.targetId];

                    if (sourcePos && targetPos) {
                        // 计算连线坐标
                        const x1 = sourcePos.x + sourcePos.width / 2 + offsetX;
                        const y1 = sourcePos.y + offsetY;
                        const x2 = targetPos.x - targetPos.width / 2 + offsetX;
                        const y2 = targetPos.y + offsetY;

                        // 创建路径
                        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                        path.setAttribute('d', calcPath(x1, y1, x2 - 15, y2));
                        path.setAttribute('stroke', 'var(--color-border-outline)');
                        path.setAttribute('stroke-width', '2');
                        path.setAttribute('fill', 'none');
                        path.setAttribute('marker-end', 'url(#modal-arrowhead)');

                        g.appendChild(path);
                        drawnLinks.add(linkKey);
                    }
                }
            });

            if (linksToProcess.length < allLinks.length) {
                console.log(`Performance optimization: Drew ${linksToProcess.length} out of ${allLinks.length} links`);
            }
        }, 100);

    } else {
        // 使用原有的布局逻辑（简化版本）
        const nodeLevels = performanceMonitor('calculateNodeLevels', () =>
            calculateNodeLevels(transformedData, centerNodeId)
        );
        if (Object.keys(nodeLevels).length === 0) {
            container.textContent = 'Could not determine node levels for lineage display.';
            return;
        }

        // 新增：优化节点位置
        const optimizedLevelGroups = performanceMonitor('optimizeNodePositionsInLevels', () =>
            optimizeNodePositionsInLevels(transformedData, nodeLevels)
        );

        const levels = Object.keys(optimizedLevelGroups).map(Number).sort((a, b) => a - b);
        const minLevel = Math.min(...levels);
        const maxLevel = Math.max(...levels);

        const columnDivs = {};
        for (let i = minLevel; i <= maxLevel; i++) {
            const columnDiv = document.createElement('div');
            columnDiv.className = 'level-column';
            columnDiv.style.display = 'flex';
            columnDiv.style.flexDirection = 'column';
            columnDiv.style.alignItems = 'center';

            // 动态调整间距
            const gap = totalNodes > 200 ? '10px' : '20px';
            columnDiv.style.gap = gap;

            graphContainer.appendChild(columnDiv);
            columnDivs[i] = columnDiv;
        }

        // 按优化后的顺序添加节点
        levels.forEach(level => {
            const nodesInLevel = optimizedLevelGroups[level] || [];
            nodesInLevel.forEach(nodeId => {
                const nodeData = (transformedData.nodes || []).find(n => n.resourceId === nodeId);
                if (nodeData) {
                    const isCurrent = nodeData.resourceId === centerNodeId;
                    const htmlNode = createNodeElement(nodeData, isCurrent);

                    // 大量节点时使用更紧凑的样式
                    if (totalNodes > 200) {
                        htmlNode.style.minWidth = '200px';
                        htmlNode.style.padding = '8px 12px';
                    }

                    columnDivs[level].appendChild(htmlNode);
                    nodeElements[nodeData.resourceId] = htmlNode;
                }
            });
        });

        setTimeout(() => {
            if (displayWrapper.scrollWidth > 0 && displayWrapper.scrollHeight > 0) {
                svgCanvas.style.width = displayWrapper.scrollWidth + 'px';
                svgCanvas.style.height = displayWrapper.scrollHeight + 'px';
                svgCanvas.setAttribute('viewBox', `0 0 ${displayWrapper.scrollWidth} ${displayWrapper.scrollHeight}`);
            }

            // 只绘制下游链接
            const allLinks = transformedData.forwardAtlas?.links || [];
            const drawnLinks = new Set();

            const g = document.createElementNS('http://www.w3.org/2000/svg', 'g');
            svgCanvas.appendChild(g);

            // 性能优化：限制连线数量
            const maxLinesToDraw = Math.min(allLinks.length, 800);
            const linksToProcess = totalNodes > 200 ? allLinks.slice(0, maxLinesToDraw) : allLinks;

            linksToProcess.forEach(link => {
                const linkKey = `${link.sourceId}->${link.targetId}`;
                if (nodeElements[link.sourceId] && nodeElements[link.targetId] && !drawnLinks.has(linkKey)) {
                    drawModalLine(nodeElements[link.sourceId].id, nodeElements[link.targetId].id, g, displayWrapper);
                    drawnLinks.add(linkKey);
                }
            });

            if (linksToProcess.length < allLinks.length) {
                console.log(`Performance optimization: Drew ${linksToProcess.length} out of ${allLinks.length} links`);
            }
        }, 200);
    }
}

/**
 * Handles the gd-route-change event from the GuanData SDK.
 * Checks if the current route matches the pattern for the data flow details page.
 * @param {object} params - The route parameters provided by the SDK.
 * @param {string} params.pathname - The path of the current URL.
 * @param {string} params.search - The query string of the current URL.
 * @param {string} params.hash - The hash fragment of the current URL.
 */
function handleRouteChange(params) {
    const { pathname } = params;
    // Regex to match /data-center/data-flow/{directoryId}/{resourceId}/details
    // Captures directoryId and resourceId
    const routePattern = /\/data-center\/data-flow\/([a-zA-Z0-9]+)\/([a-zA-Z0-9]+)\/details/;
    const match = pathname.match(routePattern);

    if (match) {
        const directoryId = match[1];
        const resourceId = match[2];
        initLineagePlugin(directoryId, resourceId, params);
    } else {
        // console.log(`Route did not match for lineage plugin: ${pathname}`);
    }
}

// Register the route change listener using the GuanData SDK.
// It's assumed that 'GD' object is available globally when this script runs.
// The SDK documentation mentions a 'gd-ready' event which might be relevant
// if GD.on is not immediately available, but typically event listeners can be set up early.
if (typeof GD !== 'undefined' && typeof GD.on === 'function') {
    GD.on('gd-route-change', handleRouteChange, 'LineagePluginDataFlowDetails');
} else {
    console.warn('GD SDK not found or GD.on is not a function. Lineage plugin route listener not attached.');
    // For development/testing outside the BI environment, you might want to manually trigger:
    // setTimeout(() => handleRouteChange({ pathname: '/data-center/data-flow/e2cb2c558bb8949eab3fd5de/h9807abc6324e4be7ab5d41a/details', search: '', hash: '' }), 1000);
}

/**
 * Performs topological sorting on the selected nodes based on DAG dependencies
 * Considers only downstream dependencies since we only show downstream nodes
 * @param {Array} selectedNodeIds - Array of selected node IDs
 * @param {Object} transformedData - The transformed lineage data containing links
 * @returns {Array} - Array of node IDs sorted in topological order (dependencies first)
 */
function topologicalSort(transformedData, allSelectedResourceIds) {
    // 性能优化：对于大量节点，使用简化的排序策略
    if (allSelectedResourceIds.length > 100) {
        console.log(`Using simplified topological sort for ${allSelectedResourceIds.length} nodes`);

        // 简化策略：仅基于直接链接关系排序
        const selectedNodesMap = new Set(allSelectedResourceIds);
        const allLinks = transformedData.forwardAtlas?.links || [];

        // 预构建选中节点的连接映射
        const adjList = {};
        const inDegree = {};

        allSelectedResourceIds.forEach(id => {
            adjList[id] = [];
            inDegree[id] = 0;
        });

        // 只考虑选中节点之间的直接连接
        allLinks.forEach(link => {
            if (selectedNodesMap.has(link.sourceId) && selectedNodesMap.has(link.targetId)) {
                adjList[link.sourceId].push(link.targetId);
                inDegree[link.targetId]++;
            }
        });

        // 标准Kahn算法
        const queue = [];
        const result = [];

        allSelectedResourceIds.forEach(id => {
            if (inDegree[id] === 0) {
                queue.push(id);
            }
        });

        while (queue.length > 0) {
            const current = queue.shift();
            result.push(current);

            adjList[current].forEach(neighbor => {
                inDegree[neighbor]--;
                if (inDegree[neighbor] === 0) {
                    queue.push(neighbor);
                }
            });
        }

        // 添加任何未处理的节点（可能的环或孤立节点）
        allSelectedResourceIds.forEach(id => {
            if (!result.includes(id)) {
                result.push(id);
            }
        });

        return result;
    }

    // 原有的完整逻辑（适用于较少节点的情况）
    const allLinks = transformedData.forwardAtlas?.links || [];
    const allEtlNodes = transformedData.nodes || [];
    const allEtlNodesMap = allEtlNodes.reduce((map, node) => {
        map[node.resourceId] = node;
        return map;
    }, {});

    // 如果没有选中的节点，直接返回空数组
    if (!allSelectedResourceIds || allSelectedResourceIds.length === 0) {
        return [];
    }

    const selectedNodesSet = new Set(allSelectedResourceIds);

    // 构建邻接表，考虑间接依赖关系
    const adjList = {};
    allEtlNodes.forEach(node => {
        adjList[node.resourceId] = [];
    });

    allLinks.forEach(link => {
        if (allEtlNodesMap[link.sourceId] && allEtlNodesMap[link.targetId]) {
            adjList[link.sourceId].push(link.targetId);
        }
    });

    // 使用DFS找到选中节点之间的路径关系
    function hasPath(from, to, visited = new Set()) {
        if (from === to) return true;
        if (visited.has(from)) return false;
        visited.add(from);

        for (const neighbor of adjList[from] || []) {
            if (hasPath(neighbor, to, visited)) {
                return true;
            }
        }
        return false;
    }

    // 构建选中节点之间的依赖关系
    const selectedAdjList = {};
    const inDegree = {};

    allSelectedResourceIds.forEach(id => {
        selectedAdjList[id] = [];
        inDegree[id] = 0;
    });

    for (let i = 0; i < allSelectedResourceIds.length; i++) {
        for (let j = 0; j < allSelectedResourceIds.length; j++) {
            if (i !== j) {
                const from = allSelectedResourceIds[i];
                const to = allSelectedResourceIds[j];

                if (hasPath(from, to)) {
                    selectedAdjList[from].push(to);
                    inDegree[to]++;
                }
            }
        }
    }

    // 卡恩算法进行拓扑排序
    const queue = [];
    const result = [];

    allSelectedResourceIds.forEach(id => {
        if (inDegree[id] === 0) {
            queue.push(id);
        }
    });

    while (queue.length > 0) {
        const current = queue.shift();
        result.push(current);

        selectedAdjList[current].forEach(neighbor => {
            inDegree[neighbor]--;
            if (inDegree[neighbor] === 0) {
                queue.push(neighbor);
            }
        });
    }

    // 如果存在环，将剩余节点按原始顺序添加
    if (result.length < allSelectedResourceIds.length) {
        allSelectedResourceIds.forEach(id => {
            if (!result.includes(id)) {
                result.push(id);
            }
        });
    }

    return result;
}
