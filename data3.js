const data = {
    "crtNode": {
        "resourceId": "h9807abc6324e4be7ab5d41a",
        "uniformResourceType": "DATA_PROCESS_ETL",
        "content": {
            "name": "2",
            "inputDataSourcesCnt": 1,
            "outputDataSourceCnt": 1,
            "dirPath": "根目录/barry/GALAXY-17663",
            "updatedOn": "2025-05-15 10:42:22",
            "lastRunningOn": "2025-05-15 10:42:23",
            "lastExecuteTime": 1,
            "parentDirectoryId": "e2cb2c558bb8949eab3fd5de",
            "status": "FINISHED"
        },
        "name": "2",
        "categoryName": "CATEGORY_DATA_ETL"
    },
    "backwardAtlas": {
        "nodes": [
            {
                "resourceId": "jb30d96fa93af4419afa4612",
                "uniformResourceType": "DATA_SET_ETL",
                "content": {
                    "name": "etl-1",
                    "displayType": "DATAFLOW",
                    "rowCount": 1,
                    "columnCount": 2,
                    "createdOn": "2025-05-15 10:42:02",
                    "updatedOn": "2025-05-15 10:42:03",
                    "lastUpdateOn": "2025-05-15 10:42:03+0800",
                    "visualDataSetType": {
                        "bigCategory": "ETL",
                        "subCategory": "ETL_OUTPUT",
                        "highPerfFlag": false
                    },
                    "isHighPerformance": false,
                    "parentDirectoryId": "t1e68fd150d384d45a2566c7",
                    "dirPath": "根目录/barry/GALAXY-17663",
                    "status": "FINISHED"
                },
                "name": "etl-1",
                "categoryName": "CATEGORY_DATA_SET"
            },
            {
                "resourceId": "t1c520f4e5edd442ab8a77c9",
                "uniformResourceType": "DATA_PROCESS_ETL",
                "content": {
                    "name": "1",
                    "inputDataSourcesCnt": 1,
                    "outputDataSourceCnt": 1,
                    "dirPath": "根目录/barry/GALAXY-17663",
                    "updatedOn": "2025-05-15 10:42:01",
                    "lastRunningOn": "2025-05-15 10:42:03",
                    "lastExecuteTime": 2,
                    "parentDirectoryId": "e2cb2c558bb8949eab3fd5de",
                    "status": "FINISHED"
                },
                "name": "1",
                "categoryName": "CATEGORY_DATA_ETL"
            },
            {
                "resourceId": "taa0a9b738b1b45d2ba515bf",
                "uniformResourceType": "DATA_SET_FILE",
                "content": {
                    "name": "test-output_副本",
                    "displayType": "EXCEL",
                    "rowCount": 1,
                    "columnCount": 2,
                    "createdOn": "2025-03-20 17:00:54",
                    "updatedOn": "2025-03-20 17:00:56",
                    "lastUpdateOn": "2025-02-08 09:56:33+0800",
                    "visualDataSetType": {
                        "bigCategory": "NON",
                        "subCategory": "OTHERS",
                        "highPerfFlag": false
                    },
                    "isHighPerformance": false,
                    "parentDirectoryId": "la1ab43a45c15407ead47061",
                    "dirPath": "根目录/barry",
                    "status": "FINISHED"
                },
                "name": "test-output_副本",
                "categoryName": "CATEGORY_DATA_SET"
            }
        ],
        "links": [
            {
                "sourceId": "jb30d96fa93af4419afa4612",
                "targetId": "h9807abc6324e4be7ab5d41a",
                "sourceType": "DATA_SET_ETL",
                "targetType": "DATA_PROCESS_ETL",
                "relativeDistance": -1,
                "subLinks": []
            },
            {
                "sourceId": "t1c520f4e5edd442ab8a77c9",
                "targetId": "jb30d96fa93af4419afa4612",
                "sourceType": "DATA_PROCESS_ETL",
                "targetType": "DATA_SET_ETL",
                "relativeDistance": -2,
                "subLinks": []
            },
            {
                "sourceId": "taa0a9b738b1b45d2ba515bf",
                "targetId": "t1c520f4e5edd442ab8a77c9",
                "sourceType": "DATA_SET_FILE",
                "targetType": "DATA_PROCESS_ETL",
                "relativeDistance": -3,
                "subLinks": []
            }
        ]
    },
    "forwardAtlas": {
        "nodes": [],
        "links": []
    }
};

function generateId() {
    return [...Array(24)].map(() => Math.floor(Math.random() * 16).toString(16)).join('');
}

const forwardNodes = [];
const forwardLinks = [];
let lastNode = data.crtNode;

for (let i = 1; i <= 300; i++) {
    const newNodeId = generateId();
    let newNode;

    if (i % 2 !== 0) {
        newNode = {
            "resourceId": newNodeId,
            "uniformResourceType": "DATA_PROCESS_ETL",
            "content": {
                "name": `generated-etl-${i}`,
                "displayType": "DATAFLOW",
                "rowCount": i,
                "columnCount": i + 1,
                "createdOn": "2025-05-15 10:42:23",
                "updatedOn": "2025-05-15 10:42:24",
                "lastUpdateOn": "2025-05-15 10:42:23+0800",
                "visualDataSetType": { "bigCategory": "ETL", "subCategory": "ETL_OUTPUT", "highPerfFlag": false },
                "isHighPerformance": false,
                "parentDirectoryId": "t1e68fd150d384d45a2566c7",
                "dirPath": "根目录/barry/GALAXY-17663",
                "status": "FINISHED"
            },
            "name": `generated-etl-${i}`,
            "categoryName": "CATEGORY_DATA_ETL"
        };
    } else {
        newNode = {
            "resourceId": newNodeId,
            "uniformResourceType": "DATA_PROCESS_ETL",
            "content": {
                "name": `generated-process-${i}`,
                "inputDataSourcesCnt": 1,
                "outputDataSourceCnt": 1,
                "dirPath": "根目录/barry/GALAXY-17663",
                "updatedOn": "2025-05-15 10:42:50",
                "lastExecuteTime": 0,
                "parentDirectoryId": "e2cb2c558bb8949eab3fd5de",
                "status": "CREATED"
            },
            "name": `generated-process-${i}`,
            "categoryName": "CATEGORY_DATA_ETL"
        };
    }

    forwardNodes.push(newNode);

    const newLink = {
        "sourceId": data.crtNode.resourceId,
        "targetId": newNode.resourceId,
        "sourceType": data.crtNode.uniformResourceType,
        "targetType": newNode.uniformResourceType,
        "relativeDistance": i,
        "subLinks": []
    };

    forwardLinks.push(newLink);
}

data.forwardAtlas.nodes = forwardNodes;
data.forwardAtlas.links = forwardLinks;


module.exports = data;
