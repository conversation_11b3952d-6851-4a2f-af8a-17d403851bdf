<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Lineage Graph</title>
<style>
  body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
    display: flex;
    justify-content: center;
    align-items: flex-start; /* Align items to the top */
    min-height: 100vh;
    background-color: #f0f2f5;
    margin: 0;
    padding-top: 50px; /* Add padding to push content down a bit */
  }

  .graph-container {
    display: flex;
    align-items: center; /* Align items vertically in the center */
    position: relative; /* For line positioning */
  }

  .node {
    background-color: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    padding: 10px 15px;
    margin: 20px;
    display: flex;
    flex-direction: column; /* Stack content vertically */
    align-items: flex-start; /* Align content to the left */
    min-width: 200px; /* Minimum width for nodes */
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: relative; /* For status indicators and labels */
    border-left: 4px solid #1890ff; /* Blue left border */
  }

  .node.current-node::before {
    content: "当前 ETL";
    position: absolute;
    top: -25px; /* Position above the node */
    left: 50%;
    transform: translateX(-50%);
    background-color: #31a378; /* Green background for the label */
    color: white;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
    white-space: nowrap;
  }

  .node-header {
    display: flex;
    align-items: center;
    width: 100%; /* Ensure header takes full width */
  }

  .node-header input[type="checkbox"] {
    margin-right: 8px;
  }

  .node-name {
    font-weight: bold;
    font-size: 14px;
    color: #333;
    flex-grow: 1; /* Allow name to take available space */
  }

  .etl-badge {
    background-color: #e6f7ff;
    color: #1890ff;
    font-size: 10px;
    padding: 2px 5px;
    border-radius: 3px;
    margin-right: 5px;
    border: 1px solid #91d5ff;
  }

  .node-info {
    font-size: 12px;
    color: #888;
    display: flex;
    align-items: center;
  }

  .status-icon {
    width: 16px;
    height: 16px;
    margin-left: 8px;
    display: inline-block;
  }

  .status-icon.success {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%2352c41a" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>');
    background-repeat: no-repeat;
    background-position: center;
  }

  .status-icon.minus {
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%23888" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="8" y1="12" x2="16" y2="12"></line></svg>');
    background-repeat: no-repeat;
    background-position: center;
  }

  .branch {
    display: flex;
    flex-direction: column; /* Stack branched nodes vertically */
    align-items: flex-start; /* Align branched nodes to the left */
    margin-left: 20px; /* Indent branched nodes */
  }

  .line {
    position: absolute;
    border: 1px solid #ccc; /* Thinner, lighter line */
    z-index: -1; /* Place lines behind nodes */
  }

</style>
</head>
<body>

<div class="graph-container">
  <div class="node" id="node1">
    <div class="node-header">
      <input type="checkbox" id="cb1" checked>
      <label for="cb1" class="node-name">1</label>
    </div>
  </div>

  <div class="node current-node" id="node2" style="margin-left: 60px;"> <!-- Increased margin for spacing -->
    <div class="node-header">
      <input type="checkbox" id="cb2" checked>
      <label for="cb2" class="node-name">2</label>
    </div>
  </div>

  <div class="branch" style="margin-left: 60px;"> <!-- Increased margin for spacing -->
    <div class="node" id="node3">
      <div class="node-header">
        <input type="checkbox" id="cb3">
        <label for="cb3" class="node-name">3</label>
      </div>
    </div>

    <div class="node" id="node4" style="margin-top: 20px;"> <!-- Add margin for spacing between 3 and 4 -->
      <div class="node-header">
        <input type="checkbox" id="cb4">
        <label for="cb4" class="node-name">4</label>
      </div>
    </div>
  </div>
</div>

<svg id="svg-canvas" width="0" height="0" style="position:absolute; top:0; left:0; z-index:-1;">
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7"
    refX="0" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#ccc" />
    </marker>
  </defs>
</svg>

<script>
  function drawLine(fromNodeId, toNodeId, isBranch) {
    const fromNode = document.getElementById(fromNodeId);
    const toNode = document.getElementById(toNodeId);
    const svg = document.getElementById('svg-canvas');
    const container = document.querySelector('.graph-container');

    // Adjust SVG canvas size to cover the entire graph container
    const rectContainer = container.getBoundingClientRect();
    svg.setAttribute('width', rectContainer.width + 'px');
    svg.setAttribute('height', rectContainer.height + 'px');
    svg.style.left = container.offsetLeft + 'px';
    svg.style.top = container.offsetTop + 'px';

    const rectFrom = fromNode.getBoundingClientRect();
    const rectTo = toNode.getBoundingClientRect();

    // Calculate coordinates relative to the SVG canvas
    const x1 = rectFrom.right - container.offsetLeft;
    const y1 = rectFrom.top + rectFrom.height / 2 - container.offsetTop;
    const x2 = rectTo.left - container.offsetLeft;
    const y2 = rectTo.top + rectTo.height / 2 - container.offsetTop;

    const line = document.createElementNS('http://www.w3.org/2000/svg','line');
    line.setAttribute('x1', x1);
    line.setAttribute('y1', y1);
    line.setAttribute('x2', x2 - 5); // Shorten line to not overlap arrowhead
    line.setAttribute('stroke', '#ccc');
    line.setAttribute('stroke-width', '2');
    line.setAttribute('marker-end', 'url(#arrowhead)');

    if (isBranch) {
        // For branches from node 2 to 3 and 2 to 4
        // Create a path with a curve
        const path = document.createElementNS('http://www.w3.org/2000/svg','path');
        const midX = x1 + (x2 - x1) / 2;
        const d = `M ${x1} ${y1} C ${midX} ${y1}, ${midX} ${y2}, ${x2 -5} ${y2}`;
        path.setAttribute('d', d);
        path.setAttribute('stroke', '#ccc');
        path.setAttribute('stroke-width', '2');
        path.setAttribute('fill', 'none');
        path.setAttribute('marker-end', 'url(#arrowhead)');
        svg.appendChild(path);
    } else {
        svg.appendChild(line);
    }
  }

  window.onload = () => {
    drawLine('node1', 'node2', false);
    drawLine('node2', 'node3', true);
    drawLine('node2', 'node4', true);
  };
  window.onresize = () => {
    // Clear existing lines
    const svg = document.getElementById('svg-canvas');
    while (svg.firstChild && svg.firstChild.tagName !== 'defs') {
        svg.removeChild(svg.firstChild);
    }
    // Redraw lines
    drawLine('node1', 'node2', false);
    drawLine('node2', 'node3', true);
    drawLine('node2', 'node4', true);
  };
</script>

</body>
</html>
