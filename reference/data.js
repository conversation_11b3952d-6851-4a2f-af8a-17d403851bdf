const data = {
    "crtNode": {
        "resourceId": "h9807abc6324e4be7ab5d41a",
        "uniformResourceType": "DATA_PROCESS_ETL",
        "content": {
            "name": "2",
            "inputDataSourcesCnt": 1,
            "outputDataSourceCnt": 1,
            "dirPath": "根目录/barry/GALAXY-17663",
            "updatedOn": "2025-05-15 10:42:22",
            "lastRunningOn": "2025-05-15 10:42:23",
            "lastExecuteTime": 1,
            "parentDirectoryId": "e2cb2c558bb8949eab3fd5de",
            "status": "FINISHED"
        },
        "name": "2",
        "categoryName": "CATEGORY_DATA_ETL"
    },
    "backwardAtlas": {
        "nodes": [
            {
                "resourceId": "jb30d96fa93af4419afa4612",
                "uniformResourceType": "DATA_SET_ETL",
                "content": {
                    "name": "etl-1",
                    "displayType": "DATAFLOW",
                    "rowCount": 1,
                    "columnCount": 2,
                    "createdOn": "2025-05-15 10:42:02",
                    "updatedOn": "2025-05-15 10:42:03",
                    "lastUpdateOn": "2025-05-15 10:42:03+0800",
                    "visualDataSetType": {
                        "bigCategory": "ETL",
                        "subCategory": "ETL_OUTPUT",
                        "highPerfFlag": false
                    },
                    "isHighPerformance": false,
                    "parentDirectoryId": "t1e68fd150d384d45a2566c7",
                    "dirPath": "根目录/barry/GALAXY-17663",
                    "status": "FINISHED"
                },
                "name": "etl-1",
                "categoryName": "CATEGORY_DATA_SET"
            },
            {
                "resourceId": "t1c520f4e5edd442ab8a77c9",
                "uniformResourceType": "DATA_PROCESS_ETL",
                "content": {
                    "name": "1",
                    "inputDataSourcesCnt": 1,
                    "outputDataSourceCnt": 1,
                    "dirPath": "根目录/barry/GALAXY-17663",
                    "updatedOn": "2025-05-15 10:42:01",
                    "lastRunningOn": "2025-05-15 10:42:03",
                    "lastExecuteTime": 2,
                    "parentDirectoryId": "e2cb2c558bb8949eab3fd5de",
                    "status": "FINISHED"
                },
                "name": "1",
                "categoryName": "CATEGORY_DATA_ETL"
            },
            {
                "resourceId": "taa0a9b738b1b45d2ba515bf",
                "uniformResourceType": "DATA_SET_FILE",
                "content": {
                    "name": "test-output_副本",
                    "displayType": "EXCEL",
                    "rowCount": 1,
                    "columnCount": 2,
                    "createdOn": "2025-03-20 17:00:54",
                    "updatedOn": "2025-03-20 17:00:56",
                    "lastUpdateOn": "2025-02-08 09:56:33+0800",
                    "visualDataSetType": {
                        "bigCategory": "NON",
                        "subCategory": "OTHERS",
                        "highPerfFlag": false
                    },
                    "isHighPerformance": false,
                    "parentDirectoryId": "la1ab43a45c15407ead47061",
                    "dirPath": "根目录/barry",
                    "status": "FINISHED"
                },
                "name": "test-output_副本",
                "categoryName": "CATEGORY_DATA_SET"
            }
        ],
        "links": [
            {
                "sourceId": "jb30d96fa93af4419afa4612",
                "targetId": "h9807abc6324e4be7ab5d41a",
                "sourceType": "DATA_SET_ETL",
                "targetType": "DATA_PROCESS_ETL",
                "relativeDistance": -1,
                "subLinks": []
            },
            {
                "sourceId": "t1c520f4e5edd442ab8a77c9",
                "targetId": "jb30d96fa93af4419afa4612",
                "sourceType": "DATA_PROCESS_ETL",
                "targetType": "DATA_SET_ETL",
                "relativeDistance": -2,
                "subLinks": []
            },
            {
                "sourceId": "taa0a9b738b1b45d2ba515bf",
                "targetId": "t1c520f4e5edd442ab8a77c9",
                "sourceType": "DATA_SET_FILE",
                "targetType": "DATA_PROCESS_ETL",
                "relativeDistance": -3,
                "subLinks": []
            }
        ]
    },
    "forwardAtlas": {
        "nodes": [
            {
                "resourceId": "x5d9deed0b3794e8cbe7becd",
                "uniformResourceType": "DATA_SET_ETL",
                "content": {
                    "name": "etl-2",
                    "displayType": "DATAFLOW",
                    "rowCount": 1,
                    "columnCount": 2,
                    "createdOn": "2025-05-15 10:42:23",
                    "updatedOn": "2025-05-15 10:42:24",
                    "lastUpdateOn": "2025-05-15 10:42:23+0800",
                    "visualDataSetType": {
                        "bigCategory": "ETL",
                        "subCategory": "ETL_OUTPUT",
                        "highPerfFlag": false
                    },
                    "isHighPerformance": false,
                    "parentDirectoryId": "t1e68fd150d384d45a2566c7",
                    "dirPath": "根目录/barry/GALAXY-17663",
                    "status": "FINISHED"
                },
                "name": "etl-2",
                "categoryName": "CATEGORY_DATA_SET"
            },
            {
                "resourceId": "m5e014973c0a143f9b3ccb38",
                "uniformResourceType": "DATA_PROCESS_ETL",
                "content": {
                    "name": "3",
                    "inputDataSourcesCnt": 1,
                    "outputDataSourceCnt": 1,
                    "dirPath": "根目录/barry/GALAXY-17663",
                    "updatedOn": "2025-05-15 10:42:50",
                    "lastExecuteTime": 0,
                    "parentDirectoryId": "e2cb2c558bb8949eab3fd5de",
                    "status": "CREATED"
                },
                "name": "3",
                "categoryName": "CATEGORY_DATA_ETL"
            },
            {
                "resourceId": "ab9e535a06c924ade972dbb2",
                "uniformResourceType": "DATA_PROCESS_ETL",
                "content": {
                    "name": "4",
                    "inputDataSourcesCnt": 1,
                    "outputDataSourceCnt": 1,
                    "dirPath": "根目录/barry/GALAXY-17663",
                    "updatedOn": "2025-05-15 10:43:08",
                    "lastExecuteTime": 0,
                    "parentDirectoryId": "e2cb2c558bb8949eab3fd5de",
                    "status": "CREATED"
                },
                "name": "4",
                "categoryName": "CATEGORY_DATA_ETL"
            }
        ],
        "links": [
            {
                "sourceId": "h9807abc6324e4be7ab5d41a",
                "targetId": "x5d9deed0b3794e8cbe7becd",
                "sourceType": "DATA_PROCESS_ETL",
                "targetType": "DATA_SET_ETL",
                "relativeDistance": 1,
                "subLinks": []
            },
            {
                "sourceId": "x5d9deed0b3794e8cbe7becd",
                "targetId": "m5e014973c0a143f9b3ccb38",
                "sourceType": "DATA_SET_ETL",
                "targetType": "DATA_PROCESS_ETL",
                "relativeDistance": 2,
                "subLinks": []
            },
            {
                "sourceId": "x5d9deed0b3794e8cbe7becd",
                "targetId": "ab9e535a06c924ade972dbb2",
                "sourceType": "DATA_SET_ETL",
                "targetType": "DATA_PROCESS_ETL",
                "relativeDistance": 2,
                "subLinks": []
            }
        ]
    }
}

module.exports = data;
