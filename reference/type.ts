interface AtlasNode {
    resourceId: string;
    uniformResourceType: string;
    content: any;
    name: string;
    categoryName: string;
  }

  interface AtlasLink {
    sourceId: string;
    targetId: string;
    sourceType: string;
    targetType: string;
    relativeDistance: number;
    subLinks: any[]; // 如果有更具体的结构可替换 any
  }

  interface AtlasGraph {
    nodes: AtlasNode[];
    links: AtlasLink[];
  }

  interface RootStructure {
    crtNode: AtlasNode;
    backwardAtlas: AtlasGraph;
    forwardAtlas: AtlasGraph;
  }
